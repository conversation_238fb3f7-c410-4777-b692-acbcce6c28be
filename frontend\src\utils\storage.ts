// 本地存储工具函数

import { STORAGE_KEYS } from './constants'

// 存储类型
type StorageType = 'localStorage' | 'sessionStorage'

/**
 * 通用存储设置函数
 */
const setStorageItem = (storage: Storage, key: string, value: any): void => {
  try {
    const serializedValue = JSON.stringify(value)
    storage.setItem(key, serializedValue)
  } catch (error) {
    console.error(`Error setting ${storage === localStorage ? 'localStorage' : 'sessionStorage'}:`, error)
  }
}

/**
 * 通用存储获取函数
 */
const getStorageItem = <T = any>(storage: Storage, key: string, defaultValue?: T): T | null => {
  try {
    const item = storage.getItem(key)
    if (item === null) {
      return defaultValue || null
    }
    return JSON.parse(item)
  } catch (error) {
    console.error(`Error getting ${storage === localStorage ? 'localStorage' : 'sessionStorage'}:`, error)
    return defaultValue || null
  }
}

/**
 * 设置localStorage
 */
export const setStorage = (key: string, value: any): void => {
  setStorageItem(localStorage, key, value)
}

/**
 * 获取localStorage
 */
export const getStorage = <T = any>(key: string, defaultValue?: T): T | null => {
  return getStorageItem<T>(localStorage, key, defaultValue)
}

/**
 * 删除localStorage
 */
export const removeStorage = (key: string): void => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing localStorage:', error)
  }
}

/**
 * 清空localStorage
 */
export const clearStorage = (): void => {
  try {
    localStorage.clear()
  } catch (error) {
    console.error('Error clearing localStorage:', error)
  }
}

/**
 * 设置sessionStorage
 */
export const setSessionStorage = (key: string, value: any): void => {
  setStorageItem(sessionStorage, key, value)
}

/**
 * 获取sessionStorage
 */
export const getSessionStorage = <T = any>(key: string, defaultValue?: T): T | null => {
  return getStorageItem<T>(sessionStorage, key, defaultValue)
}

// 便捷方法
export const token = {
  get: () => getStorage<string>(STORAGE_KEYS.TOKEN),
  set: (value: string) => setStorage(STORAGE_KEYS.TOKEN, value),
  remove: () => removeStorage(STORAGE_KEYS.TOKEN)
}

export const userInfo = {
  get: () => getStorage(STORAGE_KEYS.USER_INFO),
  set: (value: any) => setStorage(STORAGE_KEYS.USER_INFO, value),
  remove: () => removeStorage(STORAGE_KEYS.USER_INFO)
}
