// SCSS Mixins

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// 居中对齐
@mixin center($direction: both) {
  position: absolute;
  @if $direction == both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $direction == horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $direction == vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

// Flex布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// 按钮样式 - 使用固定颜色值避免CSS变量问题
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;

  &:hover {
    color: $color;
    background-color: $background;
    border-color: $border;
    opacity: 0.8;
    transform: translateY(-1px);
  }

  &:active {
    color: $color;
    background-color: $background;
    border-color: $border;
    opacity: 0.9;
    transform: translateY(0);
  }
}

// 卡片阴影
@mixin card-shadow($level: 1) {
  @if $level == 1 {
    box-shadow: $box-shadow-light;
  } @else if $level == 2 {
    box-shadow: $box-shadow-base;
  } @else if $level == 3 {
    box-shadow: $box-shadow-dark;
  }
}

// ===========================================
// 零间距 Mixins - Zero Spacing Mixins
// ===========================================

@mixin no-spacing {
  margin: 0 !important;
  padding: 0 !important;
}

@mixin no-border {
  border: none !important;
}

@mixin no-shadow {
  box-shadow: none !important;
}

@mixin no-radius {
  border-radius: 0 !important;
}

@mixin reset-element {
  @include no-spacing;
  @include no-border;
  @include no-shadow;
  @include no-radius;
}

// Element Plus 组件重置
@mixin reset-el-card {
  :deep(.el-card) {
    @include reset-element;
  }

  :deep(.el-card__body) {
    @include no-spacing;
  }
}

@mixin reset-el-form {
  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }

  :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
}

@mixin reset-el-input {
  :deep(.el-input__wrapper) {
    @include no-shadow;
    @include no-radius;
  }
}

// Flexbox 工具 Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// 容器重置 Mixin
@mixin container-reset {
  @include no-spacing;
  @include no-border;
  @include no-shadow;
  @include no-radius;

  > * {
    @include no-spacing;
  }
}
