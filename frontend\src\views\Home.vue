<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card shadow="never" class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>欢迎使用 ERP 管理系统</h1>
            <p>高效管理您的业务流程，提升企业运营效率</p>
          </div>
          <div class="welcome-icon">
            <el-icon size="80" color="#409EFF">
              <House />
            </el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions">
      <h3>快捷操作</h3>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card shadow="hover" class="action-card" @click="goToOrders">
            <div class="action-content">
              <el-icon size="32" color="#67C23A">
                <ShoppingCart />
              </el-icon>
              <h4>订单管理</h4>
              <p>查看和管理所有订单</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="action-card">
            <div class="action-content">
              <el-icon size="32" color="#E6A23C">
                <Box />
              </el-icon>
              <h4>货品管理</h4>
              <p>管理商品信息和库存</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="action-card">
            <div class="action-content">
              <el-icon size="32" color="#F56C6C">
                <PieChart />
              </el-icon>
              <h4>数据报表</h4>
              <p>查看业务数据统计</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="action-card">
            <div class="action-content">
              <el-icon size="32" color="#909399">
                <Setting />
              </el-icon>
              <h4>系统设置</h4>
              <p>配置系统参数</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据概览区域 -->
    <div class="overview-section">
      <h3>数据概览</h3>
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalOrders }}</div>
              <div class="stat-label">总订单数</div>
              <div class="stat-trend positive">+12%</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-number">¥{{ stats.totalRevenue }}</div>
              <div class="stat-label">总营收</div>
              <div class="stat-trend positive">+8%</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalProducts }}</div>
              <div class="stat-label">商品总数</div>
              <div class="stat-trend neutral">0%</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="never" class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalCustomers }}</div>
              <div class="stat-label">客户总数</div>
              <div class="stat-trend positive">+5%</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动区域 -->
    <div class="recent-activity">
      <h3>最近活动</h3>
      <el-card shadow="never">
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :type="activity.type"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Icons } from '@/utils/icons'

// 解构需要的图标
const { House, ShoppingCart, Box, PieChart, Setting } = Icons

const router = useRouter()

// 统计数据
const stats = ref({
  totalOrders: 1248,
  totalRevenue: '156,890',
  totalProducts: 89,
  totalCustomers: 456
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    content: '新订单 ORD202401006 已创建',
    time: '2024-01-17 14:30',
    type: 'primary'
  },
  {
    id: 2,
    content: '商品 "iPhone 15 Pro" 库存不足',
    time: '2024-01-17 13:45',
    type: 'warning'
  },
  {
    id: 3,
    content: '客户 "张三" 申请售后服务',
    time: '2024-01-17 12:20',
    type: 'info'
  },
  {
    id: 4,
    content: '订单 ORD202401005 已完成',
    time: '2024-01-17 11:15',
    type: 'success'
  }
])

// 跳转到订单页面
const goToOrders = () => {
  router.push('/sales/orders-new')
}
</script>

<style scoped>
.home-container {
  padding: 0;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.welcome-card :deep(.el-card__body) {
  padding: 40px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.welcome-text h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.quick-actions,
.overview-section,
.recent-activity {
  margin-bottom: 24px;
}

.quick-actions,
.overview-section,
.recent-activity {
  margin-bottom: 24px;
}

.quick-actions h3,
.overview-section h3,
.recent-activity h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.action-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.action-card:hover {
  transform: translateY(-2px);
}

.action-content {
  text-align: center;
  padding: 16px;
}

.action-content h4 {
  margin: 12px 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.action-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stat-card {
  border: 1px solid #e4e7ed;
}

.stat-content {
  text-align: center;
  padding: 16px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-trend {
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: #67C23A;
}

.stat-trend.negative {
  color: #F56C6C;
}

.stat-trend.neutral {
  color: #909399;
}

.recent-activity :deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}
</style>