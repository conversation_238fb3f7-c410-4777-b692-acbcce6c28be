<template>
  <div class="order-list-container">
    <!-- 快筛徽章标签栏 -->
    <QuickFilterBadges
      ref="quickFilterRef"
      @status-filter-change="handleStatusFilterChange"
      @time-filter-change="handleTimeFilterChange"
    />

    <!-- 搜索筛选栏 -->
    <div class="search-filter-bar">
      <el-input v-model="orderNo" placeholder="外/内部订单号" clearable />
      <el-input v-model="trackingNo" placeholder="快递单号" clearable />
      <el-input v-model="phone" placeholder="手机/座机" clearable />
      <el-input v-model="recipient" placeholder="收件人" clearable />
      <el-input v-model="productCode" placeholder="货号" clearable />
      <el-input v-model="size" placeholder="尺码" clearable />
      <el-select v-model="status" placeholder="状态" clearable>
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="platform" placeholder="平台" clearable>
        <el-option v-for="item in platformOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="shop" placeholder="店铺" clearable>
        <el-option v-for="item in shopOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="channel" placeholder="渠道" clearable>
        <el-option v-for="item in channelOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="warehouse" placeholder="仓库" clearable>
        <el-option v-for="item in warehouseOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="brand" placeholder="品牌" clearable>
        <el-option v-for="item in brandOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="logistics" placeholder="快递" clearable>
        <el-option v-for="item in logisticsOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="payment" placeholder="支付" clearable>
        <el-option v-for="item in paymentOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <SearchableSelect
        v-model="country"
        :options="countryOptions"
        placeholder="国家"
        :clearable="true"
        @change="handleCountryChange"
      />
      <SearchableSelect
        v-model="province"
        :options="provinceOptions"
        placeholder="省份"
        :clearable="true"
        @change="handleProvinceChange"
      />
      <SearchableSelect
        v-model="city"
        :options="cityOptions"
        placeholder="城市"
        :clearable="true"
        @change="handleCityChange"
      />
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </div>

    <!-- 操作按钮标签栏 -->
    <el-card shadow="never" class="action-bar">
      <div class="action-bar-content">
        <el-button type="primary" size="small" :disabled="selectedRows.length === 0">批量审单</el-button>
        <el-button type="success" size="small" :disabled="selectedRows.length === 0">批量发货</el-button>
        <el-button type="danger" size="small" :disabled="selectedRows.length === 0">批量关闭</el-button>
        <el-button type="primary" size="small" @click="showAddOrderModal">新增订单</el-button>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <div class="table-section no-margin">
      <el-card shadow="never" class="table-card no-border">
        <el-table
          :data="filteredOrders"
          v-loading="loading"
          stripe
          style="width: 100%"
          height="100%"
          table-layout="auto"
          row-key="id"
          class="internal-border-right"
          @selection-change="handleSelectionChange"
        >
          <!-- 复选框 -->
          <el-table-column
            type="selection"
            width="40"
            align="center"
            class-name="selection-cell-vertical-center"
          />
          
          <!-- 订单号 -->
          <el-table-column label="订单号" min-width="160" align="center">
            <template #default="{ row }">
              <div class="order-no-cell">
                <div class="internal-order">{{ row.orderInfo.internalOrderNo }}</div>
                <div class="external-order">{{ row.orderInfo.externalOrderNo }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 订单来源 -->
          <el-table-column label="订单来源" min-width="120" align="center">
            <template #default="{ row }">
              <div class="order-source-cell">
                <div class="shop-name">{{ row.orderSource.shop }}</div>
                <div class="platform-name">{{ row.orderSource.platform }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 商品规格 -->
          <el-table-column label="商品规格" min-width="120" align="center">
            <template #default="{ row }">
              <div class="product-spec-cell">
                <div class="product-code">{{ row.productSpec.productCode }}</div>
                <div class="product-size">{{ row.productSpec.size }}</div>
                <div class="product-quantity">{{ row.productSpec.quantity }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 仓库 -->
          <el-table-column label="仓库" min-width="120" align="center">
            <template #default="{ row }">
              <div class="warehouse-cell">
                <div class="warehouse-name">{{ row.warehouse.warehouse }}</div>
                <div class="channel-name">{{ row.warehouse.channel }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 收货信息 -->
          <el-table-column label="收货信息" min-width="160" align="left" header-align="center">
            <template #default="{ row }">
              <div class="shipping-info-cell">
                <div class="copy-btn-container">
                  <el-button 
                    @click="copyShippingInfo(row)"
                    class="copy-btn"
                  >
                    复制
                  </el-button>
                </div>
                <div class="recipient">{{ row.shippingInfo.recipient }}</div>
                <div class="phone">{{ row.shippingInfo.phone }}</div>
                <div class="address">{{ row.shippingInfo.address }}</div>
                <div class="internal-order-ref">{{ row.orderInfo.internalOrderNo }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 状态 -->
          <el-table-column label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ statusMap[row.status] || '未知状态' }}</el-tag>
            </template>
          </el-table-column>

          <!-- 物流信息 -->
          <el-table-column label="物流信息" min-width="140" align="center">
            <template #default="{ row }">
              <div class="logistics-cell">
                <div class="logistics-company">{{ row.logistics.company || '-' }}</div>
                <div class="tracking-no">{{ row.logistics.trackingNo || '-' }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 金额 -->
          <el-table-column label="金额" min-width="150" align="left" header-align="center">
            <template #default="{ row }">
              <div class="amount-cell">
                <div class="amount-item"><span class="label">单价:</span> {{ row.amount.unitPrice.toFixed(2) }}</div>
                <div class="amount-item"><span class="label">结算:</span> {{ row.amount.settlement.toFixed(2) }}</div>
                <div class="amount-item"><span class="label">采购:</span> {{ row.amount.purchase.toFixed(2) }}</div>
                <div class="amount-item"><span class="label">利润:</span> {{ row.amount.profit.toFixed(2) }}</div>
                <div class="amount-item"><span class="label">利润率:</span> {{ row.amount.profitRate.toFixed(2) }}%</div>
                <div class="amount-item"><span class="label">实付:</span> {{ row.amount.actualPaid.toFixed(2) }}</div>
                <div class="amount-item"><span class="label">总价:</span> {{ row.amount.totalPrice.toFixed(2) }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 备注 -->
          <el-table-column label="备注" min-width="140" align="left" header-align="center">
            <template #default="{ row }">
              <div class="remarks-cell">
                <div class="remark-item"><span class="label">买家:</span> {{ row.remarks.buyer }}</div>
                <div class="remark-item"><span class="label">内部:</span> {{ row.remarks.internal }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 时间信息 -->
          <el-table-column label="时间信息" min-width="180" align="left" header-align="center">
            <template #default="{ row }">
              <div class="time-info-cell">
                <div class="time-item"><span class="label">下单:</span> {{ row.timeInfo.orderTime }}</div>
                <div class="time-item"><span class="label">支付:</span> {{ row.timeInfo.payTime }}</div>
                <div class="time-item"><span class="label">创建:</span> {{ row.timeInfo.createTime }}</div>
                <div class="time-item"><span class="label">超时:</span> {{ row.timeInfo.timeoutTime }}</div>
                <div class="time-item"><span class="label">发货:</span> {{ row.timeInfo.shipTime || '-' }}</div>
                <div class="time-item"><span class="label">售后:</span> {{ row.timeInfo.afterSaleTime || '-' }}</div>
                <div class="time-item"><span class="label">倒计:</span> {{ row.timeInfo.countdownTime || '-' }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- 国家 -->
          <el-table-column label="国家" min-width="80" align="center">
            <template #default="{ row }">
              {{ row.country }}
            </template>
          </el-table-column>

          <!-- 操作 -->
          <el-table-column label="操作" min-width="180" align="center">
            <template #default="{ row }">
              <div class="operations-cell">
                <div class="operation-row">
                  <el-button type="primary" size="small" link @click="handleOperation(row, '详情')">详情</el-button>
                  <el-button type="success" size="small" link @click="handleOperation(row, '审单')">审单</el-button>
                  <el-button type="warning" size="small" link @click="handleOperation(row, '配货')">配货</el-button>
                </div>
                <div class="operation-row">
                  <el-button type="info" size="small" link @click="handleOperation(row, '售后')">售后</el-button>
                  <el-button type="primary" size="small" link @click="handleOperation(row, '改货')">改货</el-button>
                  <el-button type="warning" size="small" link @click="handleOperation(row, '改地址')">改地址</el-button>
                </div>
                <div class="operation-row">
                  <el-button type="success" size="small" link @click="handleOperation(row, '发货')">发货</el-button>
                  <el-button type="info" size="small" link @click="handleOperation(row, '备注')">备注</el-button>
                  <el-button type="primary" size="small" link @click="handleOperation(row, '日志')">日志</el-button>
                </div>
                <div class="operation-row">
                  <el-button type="danger" size="small" link @click="handleOperation(row, '关闭')">关闭</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 新增订单模态框 -->
    <AddOrderModal
      v-model:visible="addOrderModalVisible"
      @confirm-add="handleAddOrder"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import AddOrderModal from '@/components/AddOrderModal.vue'
import QuickFilterBadges from '@/components/QuickFilterBadges.vue'; // 导入组件
import SearchableSelect from '@/components/SearchableSelect.vue';
import type { Order } from '@/types/order'

const loading = ref(false)

// 快筛徽章组件引用
const quickFilterRef = ref()

// 筛选数据
const activeStatusFilter = ref('all')
const activeTimeFilter = ref('all')

const handleStatusFilterChange = (statusId: string) => {
  activeStatusFilter.value = statusId
  // 在实际应用中，这里会触发数据重新加载
  console.log('Status filter changed to:', statusId)
}

const handleTimeFilterChange = (timeId: string) => {
  activeTimeFilter.value = timeId
  // 在实际应用中，这里会触发数据重新加载
  console.log('Time filter changed to:', timeId)
}

// 搜索筛选栏 B
const orderNo = ref('')
const trackingNo = ref('')
const phone = ref('')
const recipient = ref('')
const productCode = ref('')
const size = ref('')
const status = ref('')
const platform = ref('')
const shop = ref('')
const channel = ref('')
const warehouse = ref('')
const brand = ref('')
const logistics = ref('')
const payment = ref('')
const country = ref('')
const province = ref('')
const city = ref('')
const dateRange = ref<[Date, Date] | null>(null)

// 搜索筛选栏 E

// Placeholder options for dropdowns
const statusOptions = ref([
  { label: '待审核', value: 'pending_review' },
  { label: '已审单', value: 'reviewed' },
  { label: '已发货', value: 'shipped' },
  { label: '已完成', value: 'completed' },
  { label: '已关闭', value: 'closed' },
]);

const platformOptions = ref([
  { label: '美团', value: 'meituan' },
  { label: '淘宝', value: 'taobao' },
  { label: '京东', value: 'jd' },
]);

const shopOptions = ref([
  { label: '众跑专营店', value: 'zhongpao' },
  { label: '华为官方旗舰店', value: 'huawei' },
  { label: '小米京东自营旗舰店', value: 'xiaomi' },
]);

const channelOptions = ref([
  { label: '线上渠道', value: 'online' },
  { label: '官方渠道', value: 'official' },
  { label: '自营渠道', value: 'self_operated' },
]);

const warehouseOptions = ref([
  { label: '华东仓库', value: 'huadong' },
  { label: '华南仓库', value: 'huanan' },
  { label: '华北仓库', value: 'huabei' },
]);

const brandOptions = ref([
  { label: 'Apple', value: 'apple' },
  { label: '华为', value: 'huawei' },
  { label: '小米', value: 'xiaomi' },
]);

const logisticsOptions = ref([
  { label: '顺丰速运', value: 'sf' },
  { label: '中通快递', value: 'zto' },
  { label: '京东物流', value: 'jd_logistics' },
]);

const paymentOptions = ref([
  { label: '支付宝', value: 'alipay' },
  { label: '微信支付', value: 'wechat' },
  { label: '银联', value: 'unionpay' },
  { label: '货到付款', value: 'cod' },
]);

const countryOptions = ref([
  { value: 'CN', label: '中国' },
  { value: 'US', label: '美国' },
  { value: 'JP', label: '日本' },
]);

const provinceOptions = ref<any[]>([]);
const cityOptions = ref<any[]>([]);

const handleCountryChange = (newValue: string) => {
  console.log('Country changed to:', newValue);
  province.value = '';
  city.value = '';
  // Mock data, replace with actual API call
  if (newValue === 'CN') {
    provinceOptions.value = [
      { value: 'GD', label: '广东' },
      { value: 'BJ', label: '北京' },
    ];
  } else if (newValue === 'US') {
    provinceOptions.value = [
      { value: 'CA', label: 'California' },
      { value: 'NY', label: 'New York' },
    ];
  } else {
    provinceOptions.value = [];
  }
  cityOptions.value = [];
};

const handleProvinceChange = (newValue: string) => {
  console.log('Province changed to:', newValue);
  city.value = '';
  // Mock data, replace with actual API call
  if (newValue === 'GD') {
    cityOptions.value = [
      { value: 'SZ', label: '深圳' },
      { value: 'GZ', label: '广州' },
    ];
  } else if (newValue === 'BJ') {
    cityOptions.value = [
      { value: 'BJ', label: '北京市' },
    ];
  } else if (newValue === 'CA') {
    cityOptions.value = [
        { value: 'LA', label: 'Los Angeles' },
        { value: 'SF', label: 'San Francisco' },
    ]
  } else {
    cityOptions.value = [];
  }
};

const handleCityChange = (newValue: string) => {
  console.log('City changed to:', newValue);
};


// 1. 扩展后的订单数据结构
const now = new Date();
const allOrders = ref<Order[]>([
  { id: 1, status: 'pending_review', orderInfo: { internalOrderNo: 'Z2024071201', externalOrderNo: 'MT202401001' }, deadline: new Date(now.getTime() + 0.4 * 3600 * 1000), orderSource: { shop: '众跑专营店', platform: '美团' }, productSpec: { productCode: 'IP15P256', size: '6.1英寸', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '张三', phone: '13800138001', address: '上海市 浦东新区 陆家嘴街道 世纪大道100号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 8999, settlement: 8500, purchase: 7800, profit: 700, profitRate: 8.24, actualPaid: 8999, totalPrice: 8999 }, remarks: { buyer: '要求包装精美', internal: '优质客户' }, timeInfo: { orderTime: '2024-01-15 10:30:25', payTime: '2024-01-15 10:35:18', createTime: '2024-01-15 10:30:25', timeoutTime: '2024-01-16 10:30:25', shipTime: '', afterSaleTime: '', countdownTime: '23:45:32' }, country: '中国' },
  { id: 2, status: 'reviewed', orderInfo: { internalOrderNo: 'Z2024071202', externalOrderNo: 'TB202401002' }, deadline: new Date(now.getTime() + 1.5 * 3600 * 1000), orderSource: { shop: '华为官方旗舰店', platform: '淘宝' }, productSpec: { productCode: 'HWM60P512', size: '6.82英寸', quantity: 2 }, warehouse: { warehouse: '华南仓库', channel: '官方渠道' }, shippingInfo: { recipient: '李四', phone: '13900139002', address: '广东省 深圳市 南山区 科技园街道 深南大道2000号' }, logistics: { company: '中通快递', trackingNo: 'ZT9876543210' }, amount: { unitPrice: 6999, settlement: 6800, purchase: 6200, profit: 600, profitRate: 8.82, actualPaid: 13998, totalPrice: 13998 }, remarks: { buyer: '尽快发货', internal: '重要订单' }, timeInfo: { orderTime: '2024-01-15 14:20:18', payTime: '2024-01-15 14:25:30', createTime: '2024-01-15 14:20:18', timeoutTime: '2024-01-16 14:20:18', shipTime: '2024-01-16 09:30:00', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 3, status: 'submitted', orderInfo: { internalOrderNo: 'Z2024071203', externalOrderNo: 'JD202401003' }, deadline: null, orderSource: { shop: '小米京东自营旗舰店', platform: '京东' }, productSpec: { productCode: 'MI14U1TB', size: '6.73英寸', quantity: 1 }, warehouse: { warehouse: '华北仓库', channel: '自营渠道' }, shippingInfo: { recipient: '王五', phone: '13700137003', address: '北京市 朝阳区 建国门外街道 国贸大厦A座1001室' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 6999, settlement: 6700, purchase: 6100, profit: 600, profitRate: 8.96, actualPaid: 6999, totalPrice: 6999 }, remarks: { buyer: '送货上门', internal: '新客户' }, timeInfo: { orderTime: '2024-01-16 09:15:42', payTime: '2024-01-16 09:20:15', createTime: '2024-01-16 09:15:42', timeoutTime: '2024-01-17 09:15:42', shipTime: '', afterSaleTime: '', countdownTime: '22:30:18' }, country: '中国' },
  { id: 4, status: 'exported', orderInfo: { internalOrderNo: 'Z2024071204', externalOrderNo: 'PDD202401004' }, deadline: null, orderSource: { shop: '苹果专卖店', platform: '拼多多' }, productSpec: { productCode: 'IP15P256', size: '6.1英寸', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '赵六', phone: '13600136004', address: '江苏省 南京市 玄武区 长江路1号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 8999, settlement: 8500, purchase: 7800, profit: 700, profitRate: 8.24, actualPaid: 8999, totalPrice: 8999 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-01-17 11:30:00', payTime: '2024-01-17 11:35:00', createTime: '2024-01-17 11:30:00', timeoutTime: '2024-01-18 11:30:00', shipTime: '', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 5, status: 'picking', orderInfo: { internalOrderNo: 'Z2024071205', externalOrderNo: 'DY202401005' }, deadline: new Date(now.getTime() + 3 * 3600 * 1000), orderSource: { shop: '抖音小店', platform: '抖音' }, productSpec: { productCode: 'SAM23U1TB', size: '6.8英寸', quantity: 1 }, warehouse: { warehouse: '华南仓库', channel: '线上渠道' }, shippingInfo: { recipient: '孙七', phone: '13500135005', address: '浙江省 杭州市 西湖区 文三路300号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 7999, settlement: 7500, purchase: 7000, profit: 500, profitRate: 6.25, actualPaid: 7999, totalPrice: 7999 }, remarks: { buyer: '加急', internal: '' }, timeInfo: { orderTime: '2024-01-18 15:00:00', payTime: '2024-01-18 15:05:00', createTime: '2024-01-18 15:00:00', timeoutTime: '2024-01-19 15:00:00', shipTime: '', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 6, status: 'completed', orderInfo: { internalOrderNo: 'Z2024071206', externalOrderNo: 'MT202401006' }, deadline: null, orderSource: { shop: '众跑专营店', platform: '美团' }, productSpec: { productCode: 'IP15P256', size: '6.1英寸', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '周八', phone: '13400134006', address: '北京市 海淀区 中关村大街1号' }, logistics: { company: '顺丰速运', trackingNo: 'SF1234567891' }, amount: { unitPrice: 8999, settlement: 8500, purchase: 7800, profit: 700, profitRate: 8.24, actualPaid: 8999, totalPrice: 8999 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-01-19 10:00:00', payTime: '2024-01-19 10:05:00', createTime: '2024-01-19 10:00:00', timeoutTime: '', shipTime: '2024-01-19 18:00:00', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 7, status: 'after_sales', orderInfo: { internalOrderNo: 'Z2024071207', externalOrderNo: 'TB202401007' }, deadline: null, orderSource: { shop: '华为官方旗舰店', platform: '淘宝' }, productSpec: { productCode: 'HWM60P512', size: '6.82英寸', quantity: 1 }, warehouse: { warehouse: '华南仓库', channel: '官方渠道' }, shippingInfo: { recipient: '吴九', phone: '13300133007', address: '四川省 成都市 武侯区 天府大道中段500号' }, logistics: { company: '中通快递', trackingNo: 'ZT9876543211' }, amount: { unitPrice: 6999, settlement: 6800, purchase: 6200, profit: 600, profitRate: 8.82, actualPaid: 6999, totalPrice: 6999 }, remarks: { buyer: '申请退款', internal: '跟进中' }, timeInfo: { orderTime: '2024-01-20 12:00:00', payTime: '2024-01-20 12:05:00', createTime: '2024-01-20 12:00:00', timeoutTime: '', shipTime: '2024-01-20 20:00:00', afterSaleTime: '2024-01-21 09:00:00', countdownTime: '' }, country: '中国' },
  { id: 8, status: 'shipped', orderInfo: { internalOrderNo: 'Z2024071208', externalOrderNo: 'JD202401008' }, deadline: null, isVirtual: false, shippedAt: now, orderSource: { shop: '小米京东自营旗舰店', platform: '京东' }, productSpec: { productCode: 'MI14U1TB', size: '6.73英寸', quantity: 1 }, warehouse: { warehouse: '华北仓库', channel: '自营渠道' }, shippingInfo: { recipient: '郑十', phone: '13200132008', address: '湖北省 武汉市 洪山区 珞喻路1037号' }, logistics: { company: '京东快递', trackingNo: 'JD123456789' }, amount: { unitPrice: 6999, settlement: 6700, purchase: 6100, profit: 600, profitRate: 8.96, actualPaid: 6999, totalPrice: 6999 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-01-21 14:00:00', payTime: '2024-01-21 14:05:00', createTime: '2024-01-21 14:00:00', timeoutTime: '', shipTime: '2024-01-21 22:00:00', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 9, status: 'pending_review', orderInfo: { internalOrderNo: 'Z2024071209', externalOrderNo: 'MT202401009' }, deadline: new Date(now.getTime() - 3600 * 1000), orderSource: { shop: '众跑专营店', platform: '美团' }, productSpec: { productCode: 'IP15P256', size: '6.1英寸', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '冯十一', phone: '13100131009', address: '湖南省 长沙市 岳麓区 麓山南路932号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 8999, settlement: 8500, purchase: 7800, profit: 700, profitRate: 8.24, actualPaid: 8999, totalPrice: 8999 }, remarks: { buyer: '', internal: '已超时' }, timeInfo: { orderTime: '2024-01-22 09:00:00', payTime: '2024-01-22 09:05:00', createTime: '2024-01-22 09:00:00', timeoutTime: '2024-01-22 08:00:00', shipTime: '', afterSaleTime: '', countdownTime: '' }, country: '中国' }, // 已超时
  { id: 10, status: 'pending_shipping', orderInfo: { internalOrderNo: 'Z2024071210', externalOrderNo: 'TB202401010' }, deadline: new Date(now.getTime() + 7 * 3600 * 1000), orderSource: { shop: '华为官方旗舰店', platform: '淘宝' }, productSpec: { productCode: 'HWM60P512', size: '6.82英寸', quantity: 1 }, warehouse: { warehouse: '华南仓库', channel: '官方渠道' }, shippingInfo: { recipient: '陈十二', phone: '13000130010', address: '陕西省 西安市 雁塔区 长安中路222号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 6999, settlement: 6800, purchase: 6200, profit: 600, profitRate: 8.82, actualPaid: 6999, totalPrice: 6999 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-01-23 16:00:00', payTime: '2024-01-23 16:05:00', createTime: '2024-01-23 16:00:00', timeoutTime: '2024-01-23 23:00:00', shipTime: '', afterSaleTime: '', countdownTime: '' }, country: '中国' }, // ~7小时后超时
  { id: 11, status: 'shipped', orderInfo: { internalOrderNo: 'Z2024071211', externalOrderNo: 'JD202401011' }, deadline: null, isVirtual: true, shippedAt: new Date(now.getTime() - 10 * 3600 * 1000), orderSource: { shop: '小米京东自营旗舰店', platform: '京东' }, productSpec: { productCode: 'MI14U1TB', size: '6.73英寸', quantity: 1 }, warehouse: { warehouse: '华北仓库', channel: '自营渠道' }, shippingInfo: { recipient: '褚十三', phone: '15900159011', address: '山东省 青岛市 市南区 香港中路8号' }, logistics: { company: '京东快递', trackingNo: 'JDV123456789' }, amount: { unitPrice: 6999, settlement: 6700, purchase: 6100, profit: 600, profitRate: 8.96, actualPaid: 6999, totalPrice: 6999 }, remarks: { buyer: '虚拟发货', internal: '' }, timeInfo: { orderTime: '2024-01-24 18:00:00', payTime: '2024-01-24 18:05:00', createTime: '2024-01-24 18:00:00', timeoutTime: '', shipTime: '2024-01-24 08:00:00', afterSaleTime: '', countdownTime: '' }, country: '中国' }, // 虚拟发货，10小时前
  { id: 12, status: 'closed', orderInfo: { internalOrderNo: 'Z2024071212', externalOrderNo: 'MT202401012' }, deadline: null, orderSource: { shop: '众跑专营店', platform: '美团' }, productSpec: { productCode: 'IP15P256', size: '6.1英寸', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '卫十四', phone: '15800158012', address: '重庆市 渝中区 解放碑步行街1号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 8999, settlement: 8500, purchase: 7800, profit: 700, profitRate: 8.24, actualPaid: 0, totalPrice: 8999 }, remarks: { buyer: '用户取消', internal: '已关闭' }, timeInfo: { orderTime: '2024-01-25 20:00:00', payTime: '', createTime: '2024-01-25 20:00:00', timeoutTime: '', shipTime: '', afterSaleTime: '2024-01-25 20:10:00', countdownTime: '' }, country: '中国' }, // 新增：已关闭状态
  { id: 13, status: 'pending_review', orderInfo: { internalOrderNo: 'Z2024071213', externalOrderNo: 'PDD202401013' }, deadline: new Date(now.getTime() + 2 * 3600 * 1000), orderSource: { shop: '苹果专卖店', platform: '拼多多' }, productSpec: { productCode: 'IP15P512', size: '6.1英寸', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '蒋十五', phone: '15700157013', address: '福建省 厦门市 思明区 环岛路1号' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 9999, settlement: 9500, purchase: 8800, profit: 700, profitRate: 7.37, actualPaid: 9999, totalPrice: 9999 }, remarks: { buyer: '送保护壳', internal: '' }, timeInfo: { orderTime: '2024-01-26 11:00:00', payTime: '2024-01-26 11:05:00', createTime: '2024-01-26 11:00:00', timeoutTime: '2024-01-26 13:00:00', shipTime: '', afterSaleTime: '', countdownTime: '01:58:10' }, country: '中国' },
  { id: 14, status: 'reviewed', orderInfo: { internalOrderNo: 'Z2024071214', externalOrderNo: 'DY202401014' }, deadline: null, orderSource: { shop: '抖音小店', platform: '抖音' }, productSpec: { productCode: 'OPPOFindX7', size: '标准版', quantity: 1 }, warehouse: { warehouse: '华中仓库', channel: '线上渠道' }, shippingInfo: { recipient: '沈十六', phone: '15600156014', address: '河南省 郑州市 金水区 农业路东段' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 3999, settlement: 3800, purchase: 3500, profit: 300, profitRate: 7.89, actualPaid: 3999, totalPrice: 3999 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-01-27 10:10:10', payTime: '2024-01-27 10:15:20', createTime: '2024-01-27 10:10:10', timeoutTime: '2024-01-28 10:10:10', shipTime: '', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 15, status: 'completed', orderInfo: { internalOrderNo: 'Z2024071215', externalOrderNo: 'TB202401015' }, deadline: null, orderSource: { shop: '华为官方旗舰店', platform: '淘宝' }, productSpec: { productCode: 'HWMateBookX', size: '13英寸', quantity: 1 }, warehouse: { warehouse: '西南仓库', channel: '官方渠道' }, shippingInfo: { recipient: '韩十七', phone: '15500155015', address: '云南省 昆明市 盘龙区 东风东路88号' }, logistics: { company: '圆通速递', trackingNo: 'YT123123123' }, amount: { unitPrice: 8999, settlement: 8700, purchase: 8000, profit: 700, profitRate: 8.05, actualPaid: 8999, totalPrice: 8999 }, remarks: { buyer: '已签收', internal: '' }, timeInfo: { orderTime: '2024-01-28 14:30:00', payTime: '2024-01-28 14:32:00', createTime: '2024-01-28 14:30:00', timeoutTime: '', shipTime: '2024-01-29 09:00:00', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 16, status: 'shipped', orderInfo: { internalOrderNo: 'Z2024071216', externalOrderNo: 'JD202401016' }, deadline: null, orderSource: { shop: '小米京东自营旗舰店', platform: '京东' }, productSpec: { productCode: 'MIWatchS3', size: '黑色', quantity: 3 }, warehouse: { warehouse: '华北仓库', channel: '自营渠道' }, shippingInfo: { recipient: '杨十八', phone: '15300153016', address: '天津市 和平区 南京路189号' }, logistics: { company: '京东快递', trackingNo: 'JD987987987' }, amount: { unitPrice: 699, settlement: 650, purchase: 600, profit: 50, profitRate: 7.69, actualPaid: 2097, totalPrice: 2097 }, remarks: { buyer: '分开发票', internal: '' }, timeInfo: { orderTime: '2024-02-01 18:00:00', payTime: '2024-02-01 18:01:00', createTime: '2024-02-01 18:00:00', timeoutTime: '', shipTime: '2024-02-02 11:00:00', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 17, status: 'pending_review', orderInfo: { internalOrderNo: 'Z2024071217', externalOrderNo: 'MT202401017' }, deadline: new Date(now.getTime() + 23.5 * 3600 * 1000), orderSource: { shop: '众跑专营店', platform: '美团' }, productSpec: { productCode: 'NikeAirMax', size: '42', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '朱十九', phone: '15200152017', address: '江西省 南昌市 东湖区 八一大道' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 899, settlement: 850, purchase: 780, profit: 70, profitRate: 8.24, actualPaid: 899, totalPrice: 899 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-02-03 12:00:00', payTime: '2024-02-03 12:05:00', createTime: '2024-02-03 12:00:00', timeoutTime: '2024-02-04 12:00:00', shipTime: '', afterSaleTime: '', countdownTime: '23:30:00' }, country: '中国' },
  { id: 18, status: 'closed', orderInfo: { internalOrderNo: 'Z2024071218', externalOrderNo: 'TB202401018' }, deadline: null, orderSource: { shop: '华为官方旗舰店', platform: '淘宝' }, productSpec: { productCode: 'HWFreeClip', size: '紫色', quantity: 1 }, warehouse: { warehouse: '华南仓库', channel: '官方渠道' }, shippingInfo: { recipient: '秦二十', phone: '15100151018', address: '海南省 三亚市 吉阳区 亚龙湾' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 1299, settlement: 1200, purchase: 1100, profit: 100, profitRate: 8.33, actualPaid: 0, totalPrice: 1299 }, remarks: { buyer: '地址错误，用户要求取消', internal: '已和用户沟通' }, timeInfo: { orderTime: '2024-02-05 16:45:00', payTime: '2024-02-05 16:50:00', createTime: '2024-02-05 16:45:00', timeoutTime: '', shipTime: '', afterSaleTime: '2024-02-05 17:00:00', countdownTime: '' }, country: '中国' },
  { id: 19, status: 'after_sales', orderInfo: { internalOrderNo: 'Z2024071219', externalOrderNo: 'PDD202401019' }, deadline: null, orderSource: { shop: '苹果专卖店', platform: '拼多多' }, productSpec: { productCode: 'AirPodsPro3', size: 'Type-C', quantity: 1 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '尤二十一', phone: '15000150019', address: '安徽省 合肥市 蜀山区 黄山路' }, logistics: { company: '申通快递', trackingNo: 'STO123456' }, amount: { unitPrice: 1899, settlement: 1800, purchase: 1700, profit: 100, profitRate: 5.56, actualPaid: 1899, totalPrice: 1899 }, remarks: { buyer: '申请换货', internal: '待寄回' }, timeInfo: { orderTime: '2024-02-06 19:00:00', payTime: '2024-02-06 19:02:00', createTime: '2024-02-06 19:00:00', timeoutTime: '', shipTime: '2024-02-07 10:00:00', afterSaleTime: '2024-02-08 15:00:00', countdownTime: '' }, country: '中国' },
  { id: 20, status: 'picking', orderInfo: { internalOrderNo: 'Z2024071220', externalOrderNo: 'DY202401020' }, deadline: new Date(now.getTime() + 48 * 3600 * 1000), orderSource: { shop: '抖音小店', platform: '抖音' }, productSpec: { productCode: 'DJIMini4', size: '畅飞套装', quantity: 1 }, warehouse: { warehouse: '华南仓库', channel: '线上渠道' }, shippingInfo: { recipient: '许二十二', phone: '18900189020', address: '广西壮族自治区 南宁市 青秀区 民族大道' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 4999, settlement: 4800, purchase: 4500, profit: 300, profitRate: 6.25, actualPaid: 4999, totalPrice: 4999 }, remarks: { buyer: '需要电池', internal: '' }, timeInfo: { orderTime: '2024-02-10 20:00:00', payTime: '2024-02-10 20:05:00', createTime: '2024-02-10 20:00:00', timeoutTime: '2024-02-12 20:00:00', shipTime: '', afterSaleTime: '', countdownTime: '47:55:00' }, country: '中国' },
  { id: 21, status: 'exported', orderInfo: { internalOrderNo: 'Z2024071221', externalOrderNo: 'JD202401021' }, deadline: null, orderSource: { shop: '小米京东自营旗舰店', platform: '京东' }, productSpec: { productCode: 'RedmiK70', size: '16+512', quantity: 1 }, warehouse: { warehouse: '华北仓库', channel: '自营渠道' }, shippingInfo: { recipient: '何二十三', phone: '18800188021', address: '河北省 石家庄市 长安区 中山东路' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 2999, settlement: 2800, purchase: 2600, profit: 200, profitRate: 7.14, actualPaid: 2999, totalPrice: 2999 }, remarks: { buyer: '', internal: '' }, timeInfo: { orderTime: '2024-02-11 13:20:00', payTime: '2024-02-11 13:21:00', createTime: '2024-02-11 13:20:00', timeoutTime: '2024-02-12 13:20:00', shipTime: '', afterSaleTime: '', countdownTime: '' }, country: '中国' },
  { id: 22, status: 'submitted', orderInfo: { internalOrderNo: 'Z2024071222', externalOrderNo: 'TB202401022' }, deadline: null, orderSource: { shop: '华为官方旗舰店', platform: '淘宝' }, productSpec: { productCode: 'HUAWEIP70', size: 'Art', quantity: 1 }, warehouse: { warehouse: '华南仓库', channel: '官方渠道' }, shippingInfo: { recipient: '吕二十四', phone: '18700187022', address: '广东省 广州市 天河区 珠江新城' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 9999, settlement: 9800, purchase: 9000, profit: 800, profitRate: 8.16, actualPaid: 9999, totalPrice: 9999 }, remarks: { buyer: '尊享版', internal: '' }, timeInfo: { orderTime: '2024-02-12 10:00:00', payTime: '2024-02-12 10:00:00', createTime: '2024-02-12 10:00:00', timeoutTime: '2024-02-13 10:00:00', shipTime: '', afterSaleTime: '', countdownTime: '23:59:59' }, country: '中国' },
  { id: 23, status: 'pending_shipping', orderInfo: { internalOrderNo: 'Z2024071223', externalOrderNo: 'MT202401023' }, deadline: new Date(now.getTime() + 12 * 3600 * 1000), orderSource: { shop: '众跑专营店', platform: '美团' }, productSpec: { productCode: 'AdidasBoost', size: '43', quantity: 2 }, warehouse: { warehouse: '华东仓库', channel: '线上渠道' }, shippingInfo: { recipient: '施二十五', phone: '18600186023', address: '上海市 静安区 南京西路' }, logistics: { company: '', trackingNo: '' }, amount: { unitPrice: 1299, settlement: 1200, purchase: 1100, profit: 100, profitRate: 8.33, actualPaid: 2598, totalPrice: 2598 }, remarks: { buyer: '两双都要原盒', internal: '' }, timeInfo: { orderTime: '2024-02-14 11:11:11', payTime: '2024-02-14 11:12:11', createTime: '2024-02-14 11:11:11', timeoutTime: '2024-02-14 23:11:11', shipTime: '', afterSaleTime: '', countdownTime: '11:58:21' }, country: '中国' },
]);

// 过滤后的订单
const filteredOrders = computed(() => {
  // 这是一个简化的过滤逻辑，实际应用中会更复杂
  // 会结合 activeStatusFilter, activeTimeFilter 和所有搜索栏的字段
  return allOrders.value.filter(order => {
    // 示例：仅应用状态过滤
    const statusMatch = activeStatusFilter.value === 'all' || order.status === activeStatusFilter.value;
    
    // 你可以在这里添加更多的过滤条件
    // const timeMatch = ...
    // const orderNoMatch = ...

    return statusMatch;
  });
});


// Search Form and other existing logic...
// ...

const selectedRows = ref<any[]>([])

// 新增订单模态框
const addOrderModalVisible = ref(false)

// 处理复选框选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// Dropdown command handler
const handleDropdownCommand = (row: any, command: string) => {
  handleOperation(row, command)
}

// 生成内部订单号
const generateInternalOrderNo = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hour = String(now.getHours()).padStart(2, '0')
  const minute = String(now.getMinutes()).padStart(2, '0')
  const second = String(now.getSeconds()).padStart(2, '0')
  const counter = String(Math.floor(Math.random() * 9999999)).padStart(7, '0')
  return `Z${year}${month}${day}${hour}${minute}${second}${counter}`
}

// 复制收货信息
const copyShippingInfo = (row: any) => {
  const info = `${row.shippingInfo.recipient}\n${row.shippingInfo.phone}\n${row.shippingInfo.address}\n${row.orderInfo.internalOrderNo}`
  navigator.clipboard.writeText(info).then(() => {
    ElMessage.success('收货信息已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

// 显示新增订单模态框
const showAddOrderModal = () => {
  addOrderModalVisible.value = true
}

// 处理新增订单
const handleAddOrder = (orderData: any) => {
  console.log('新增订单数据:', orderData)

  // 生成内部订单号
  const internalOrderNo = generateInternalOrderNo()

  // 创建新订单对象
  const newOrder = {
    id: allOrders.value.length + 1,
    orderInfo: {
      internalOrderNo: internalOrderNo,
      externalOrderNo: orderData.order_number
    },
    orderSource: {
      shop: orderData.shop_id,
      platform: orderData.platform_id
    },
    productSpec: {
      productCode: orderData.product_code,
      size: orderData.size,
      quantity: orderData.quantity
    },
    priceInfo: {
      unitPrice: parseFloat(orderData.unit_price) || 0,
      totalAmount: parseFloat(orderData.total_amount) || 0,
      salePrice: parseFloat(orderData.sale_price) || 0,
      settlementAmount: parseFloat(orderData.settlement_amount) || 0
    },
    shippingInfo: {
      recipient: orderData.receiver_name,
      phone: orderData.receiver_phone,
      address: `${orderData.receiver_province}${orderData.receiver_city}${orderData.receiver_district}${orderData.receiver_street || ''}${orderData.receiver_address}`
    },
    timeInfo: {
      orderTime: orderData.order_time,
      paymentTime: orderData.paid_time,
      shippingTime: null,
      deliveryTime: null
    },
    status: 'pending_review',
    notes: {
      buyerNote: orderData.buyer_note || '',
      internalNote: orderData.internal_note || ''
    },
    deadline: new Date(new Date().getTime() + 24 * 3600 * 1000) // 默认24小时后超时
  }

  // 添加到表格数据
  allOrders.value.unshift(newOrder as any)

  // 显示成功消息
  ElMessage.success('订单添加成功！')

  // 关闭模态框
  addOrderModalVisible.value = false
}


// 状态中英文映射
const statusMap: Record<string, string> = {
  'pending_review': '待审核',
  'reviewed': '已审核',
  'submitted': '已交单',
  'exported': '已导出',
  'picking': '配货中',
  'shipped': '已发货',
  'completed': '已完成',
  'closed': '已关闭',
  'after_sales': '售后中',
  'pending_shipping': '待发货'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusTypeMap: Record<string, string> = {
    'pending_review': 'warning',
    'reviewed': 'success',
    'submitted': 'primary',
    'exported': 'info',
    'picking': 'warning',
    'shipped': 'primary',
    'completed': 'success',
    'closed': 'danger',
    'after_sales': 'warning',
    'pending_shipping': 'warning'
  }
  return statusTypeMap[status] || 'info'
}

// 处理操作按钮点击
const handleOperation = (row: any, operation: string) => {
  ElMessage.info(`执行操作：${operation}，订单号：${row.orderInfo.internalOrderNo}`)
  switch (operation) {
    case '关闭':
      ElMessageBox.confirm(
        `确定要关闭订单 ${row.orderInfo.internalOrderNo} 吗？`,
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // Find the order and update its status
        const order = allOrders.value.find(o => o.id === row.id);
        if (order) {
          order.status = 'closed';
        }
        ElMessage.success('订单已关闭')
      }).catch(() => {
        ElMessage.info('已取消关闭')
      })
      break
    default:
      break
  }
}
</script>

<style scoped lang="scss">
// 导入订单列表组件样式
@import '@/styles/components/order-list.scss';
</style>