// 动态主题样式 - 使用CSS变量的样式
// 这些样式会根据主题动态变化

// 主题相关的组件样式
.theme-dynamic {
  // 状态标签 - 使用CSS变量版本
  .status-tag {
    &.status-success {
      background-color: color-mix(in srgb, var(--theme-success) 20%, transparent);
      color: var(--theme-success);
    }
    
    &.status-warning {
      background-color: color-mix(in srgb, var(--theme-warning) 20%, transparent);
      color: var(--theme-warning);
    }
    
    &.status-danger {
      background-color: color-mix(in srgb, var(--theme-error) 20%, transparent);
      color: var(--theme-error);
    }
    
    &.status-info {
      background-color: color-mix(in srgb, var(--theme-info) 20%, transparent);
      color: var(--theme-info);
    }
  }

  // 页面容器
  .page-container {
    background-color: var(--theme-surface);
  }

  // 卡片容器
  .card-container {
    background-color: var(--theme-background);
    border: 1px solid var(--theme-border);
  }

  // 表格容器
  .table-container {
    background-color: var(--theme-background);
    border: 1px solid var(--theme-border);
    
    .el-table__header {
      background-color: var(--theme-surface);
    }
  }

  // 搜索栏
  .search-bar {
    background-color: var(--theme-background);
    border: 1px solid var(--theme-border);
  }

  // 操作栏
  .action-bar {
    background-color: var(--theme-background);
    border: 1px solid var(--theme-border);
  }

  // 文本颜色
  .text-primary {
    color: var(--theme-text);
  }

  .text-secondary {
    color: var(--theme-text-secondary);
  }

  // 加载状态
  .loading-container {
    color: var(--theme-text-secondary);
  }

  // 空状态
  .empty-container {
    color: var(--theme-text-secondary);
    
    .empty-icon {
      color: var(--theme-text-secondary);
      opacity: 0.5;
    }
    
    .empty-description {
      color: var(--theme-text-secondary);
      opacity: 0.7;
    }
  }
}

// 为不支持color-mix的浏览器提供回退
@supports not (background-color: color-mix(in srgb, red 20%, transparent)) {
  .theme-dynamic {
    .status-tag {
      &.status-success {
        background-color: rgba(103, 194, 58, 0.2);
      }
      
      &.status-warning {
        background-color: rgba(230, 162, 60, 0.2);
      }
      
      &.status-danger {
        background-color: rgba(245, 108, 108, 0.2);
      }
      
      &.status-info {
        background-color: rgba(144, 147, 153, 0.2);
      }
    }
  }
}

// 主题切换动画
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
