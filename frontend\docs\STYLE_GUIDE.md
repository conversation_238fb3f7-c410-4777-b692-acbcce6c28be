# 样式编写规范和最佳实践

## 概述

本文档定义了项目中样式编写的规范和最佳实践，旨在提高代码的可维护性、一致性和可读性。

## 目录

1. [设计系统](#设计系统)
2. [文件结构](#文件结构)
3. [命名规范](#命名规范)
4. [样式编写规则](#样式编写规则)
5. [工具类使用](#工具类使用)
6. [组件样式](#组件样式)
7. [响应式设计](#响应式设计)
8. [性能优化](#性能优化)

## 设计系统

### 变量系统

项目使用统一的设计变量系统，所有样式应优先使用预定义的变量：

```scss
// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 字体系统
$font-size-xs: 10px;
$font-size-extra-small: 12px;
$font-size-small: 13px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;

// 颜色系统
$primary-color: #409EFF;
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$bg-white: #ffffff;
$bg-light: #f5f7fa;
```

### 禁止使用硬编码值

❌ **错误示例：**
```scss
.container {
  padding: 16px;
  margin: 20px;
  color: #303133;
  font-size: 14px;
}
```

✅ **正确示例：**
```scss
.container {
  padding: $spacing-lg;
  margin: $spacing-xl;
  color: $text-primary;
  font-size: $font-size-base;
}
```

## 文件结构

```
src/styles/
├── variables.scss      # 设计变量
├── mixins.scss        # 通用 mixins
├── utilities.scss     # 工具类
├── base.scss         # 基础样式
├── components/       # 组件样式
└── themes/          # 主题样式
```

## 命名规范

### BEM 命名法

使用 BEM (Block Element Modifier) 命名法：

```scss
// Block
.order-list {}

// Element
.order-list__header {}
.order-list__content {}
.order-list__footer {}

// Modifier
.order-list--compact {}
.order-list__header--sticky {}
```

### 类名规范

- 使用 kebab-case (短横线分隔)
- 语义化命名，避免缩写
- 组件名作为前缀

```scss
// ✅ 好的命名
.search-filter-bar {}
.order-status-badge {}
.shipping-info-cell {}

// ❌ 避免的命名
.sfb {}
.osb {}
.sic {}
```

## 样式编写规则

### 1. 导入顺序

```scss
// 1. 变量和 mixins
@import '@/styles/variables.scss';
@import '@/styles/mixins.scss';

// 2. 第三方库样式
@import 'element-plus/theme-chalk/index.css';

// 3. 组件样式
```

### 2. 属性顺序

按照以下顺序编写 CSS 属性：

```scss
.example {
  // 1. 定位
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  
  // 2. 盒模型
  display: flex;
  width: 100%;
  height: auto;
  margin: 0;
  padding: $spacing-md;
  border: 1px solid $border-base;
  
  // 3. 字体和文本
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  color: $text-primary;
  text-align: left;
  
  // 4. 背景和装饰
  background-color: $bg-white;
  border-radius: $border-radius-base;
  box-shadow: $shadow-sm;
  
  // 5. 动画和过渡
  transition: $transition-base;
}
```

### 3. 嵌套规则

- 最多嵌套 3 层
- 优先使用 mixins 而不是深度嵌套

```scss
// ✅ 好的嵌套
.order-list {
  @include flex($direction: column);
  
  &__header {
    @include flex-between;
    padding: $spacing-md;
    
    .title {
      font-weight: $font-weight-bold;
    }
  }
}

// ❌ 避免过深嵌套
.order-list {
  .header {
    .title {
      .text {
        .content {
          color: red; // 太深了
        }
      }
    }
  }
}
```

## 工具类使用

### 间距工具类

优先使用工具类处理简单的间距：

```html
<!-- 使用工具类 -->
<div class="p-md m-lg">
  <div class="mt-sm mb-md">内容</div>
</div>
```

```scss
// 对应的工具类
.p-md { padding: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.mt-sm { margin-top: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
```

### 布局工具类

```html
<!-- Flexbox 布局 -->
<div class="flex flex-between items-center">
  <div class="flex-1">左侧内容</div>
  <div>右侧内容</div>
</div>
```

### 零间距工具类

对于需要零间距的容器：

```html
<div class="no-spacing">
  <div class="reset-card">卡片内容</div>
</div>
```

## 组件样式

### 1. 组件样式结构

```scss
.component-name {
  // 组件根样式
  @include component-base;
  
  // 子元素
  &__element {
    // 元素样式
  }
  
  // 修饰符
  &--modifier {
    // 修饰符样式
  }
  
  // 状态
  &.is-active,
  &.is-disabled {
    // 状态样式
  }
  
  // 深度选择器（谨慎使用）
  :deep(.el-component) {
    // 第三方组件样式覆盖
  }
}
```

### 2. 零间距设计

对于需要零间距的组件，使用专门的 mixins：

```scss
.order-list-container {
  @include container-reset; // 容器零间距
  
  .search-bar {
    @include reset-element; // 元素零间距
  }
  
  :deep(.el-card) {
    @include reset-el-card; // Element Plus 组件重置
  }
}
```

### 3. 主题适配

使用 CSS 变量支持主题切换：

```scss
.component {
  background-color: var(--theme-background);
  color: var(--theme-text);
  border-color: var(--theme-border);
}
```

## 响应式设计

### 使用响应式 Mixins

```scss
.component {
  font-size: $font-size-base;
  
  @include respond-to(sm) {
    font-size: $font-size-small;
  }
  
  @include respond-to(xs) {
    font-size: $font-size-xs;
  }
}
```

### 断点系统

```scss
$breakpoint-xs: 480px;   // 手机
$breakpoint-sm: 768px;   // 平板
$breakpoint-md: 992px;   // 小桌面
$breakpoint-lg: 1200px;  // 大桌面
$breakpoint-xl: 1600px;  // 超大屏
```

## 性能优化

### 1. 避免深度选择器

```scss
// ❌ 避免
:deep(.el-table .el-table__cell .content .text) {
  color: red;
}

// ✅ 推荐
:deep(.el-table__cell) {
  .content-text {
    color: red;
  }
}
```

### 2. 使用 CSS 变量

```scss
// ❌ 避免重复定义
.button-primary { background: #409EFF; }
.link-primary { color: #409EFF; }

// ✅ 使用变量
.button-primary { background: var(--color-primary); }
.link-primary { color: var(--color-primary); }
```

### 3. 合理使用 !important

只在覆盖第三方库样式时使用：

```scss
// ✅ 合理使用
:deep(.el-card) {
  margin: 0 !important; // 覆盖 Element Plus 默认样式
}

// ❌ 避免滥用
.my-component {
  color: red !important; // 不必要的 !important
}
```

## 最佳实践总结

1. **优先使用设计变量**：避免硬编码值
2. **使用工具类**：处理简单的样式需求
3. **合理嵌套**：最多 3 层嵌套
4. **语义化命名**：使用 BEM 命名法
5. **组件化思维**：每个组件独立的样式作用域
6. **响应式优先**：使用 mixins 处理断点
7. **性能考虑**：避免过度使用深度选择器
8. **主题支持**：使用 CSS 变量支持主题切换

## 代码审查清单

在提交样式代码前，请检查：

- [ ] 是否使用了设计变量而不是硬编码值
- [ ] 是否遵循了 BEM 命名规范
- [ ] 是否使用了合适的工具类
- [ ] 嵌套层级是否超过 3 层
- [ ] 是否有不必要的 !important
- [ ] 响应式设计是否完整
- [ ] 是否支持主题切换
- [ ] 性能是否优化
