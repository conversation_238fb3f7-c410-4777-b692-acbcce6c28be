// Vue 3 Composables - 可复用的组合式函数

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 加载状态管理
 */
export function useLoading(initialState = false) {
  const loading = ref(initialState)
  
  const setLoading = (state: boolean) => {
    loading.value = state
  }
  
  const withLoading = async <T>(fn: () => Promise<T>): Promise<T> => {
    setLoading(true)
    try {
      return await fn()
    } finally {
      setLoading(false)
    }
  }
  
  return {
    loading,
    setLoading,
    withLoading
  }
}

/**
 * 分页管理
 */
export function usePagination(pageSize = 10) {
  const currentPage = ref(1)
  const total = ref(0)
  const pageSizes = ref([10, 20, 50, 100])
  const currentPageSize = ref(pageSize)
  
  const offset = computed(() => (currentPage.value - 1) * currentPageSize.value)
  
  const handlePageChange = (page: number) => {
    currentPage.value = page
  }
  
  const handleSizeChange = (size: number) => {
    currentPageSize.value = size
    currentPage.value = 1
  }
  
  const resetPagination = () => {
    currentPage.value = 1
    total.value = 0
  }
  
  return {
    currentPage,
    currentPageSize,
    total,
    pageSizes,
    offset,
    handlePageChange,
    handleSizeChange,
    resetPagination
  }
}

/**
 * 表单验证
 */
export function useFormValidation() {
  const formRef = ref()
  const validating = ref(false)
  
  const validate = async (): Promise<boolean> => {
    if (!formRef.value) return false
    
    validating.value = true
    try {
      await formRef.value.validate()
      return true
    } catch {
      return false
    } finally {
      validating.value = false
    }
  }
  
  const resetValidation = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }
  
  const clearValidation = () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }
  
  return {
    formRef,
    validating,
    validate,
    resetValidation,
    clearValidation
  }
}

/**
 * 消息提示
 */
export function useMessage() {
  const success = (message: string) => {
    ElMessage.success(message)
  }
  
  const error = (message: string) => {
    ElMessage.error(message)
  }
  
  const warning = (message: string) => {
    ElMessage.warning(message)
  }
  
  const info = (message: string) => {
    ElMessage.info(message)
  }
  
  const confirm = async (message: string, title = '确认'): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(message, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      return true
    } catch {
      return false
    }
  }
  
  return {
    success,
    error,
    warning,
    info,
    confirm
  }
}

/**
 * 路由导航
 */
export function useNavigation() {
  const router = useRouter()
  const route = useRoute()
  
  const goBack = () => {
    router.back()
  }
  
  const goTo = (path: string) => {
    router.push(path)
  }
  
  const replace = (path: string) => {
    router.replace(path)
  }
  
  const reload = () => {
    router.go(0)
  }
  
  return {
    router,
    route,
    goBack,
    goTo,
    replace,
    reload
  }
}

/**
 * 窗口尺寸监听
 */
export function useWindowSize() {
  const width = ref(window.innerWidth)
  const height = ref(window.innerHeight)
  
  const isMobile = computed(() => width.value < 768)
  const isTablet = computed(() => width.value >= 768 && width.value < 1024)
  const isDesktop = computed(() => width.value >= 1024)
  
  const updateSize = () => {
    width.value = window.innerWidth
    height.value = window.innerHeight
  }
  
  onMounted(() => {
    window.addEventListener('resize', updateSize)
  })
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateSize)
  })
  
  return {
    width,
    height,
    isMobile,
    isTablet,
    isDesktop
  }
}

/**
 * 本地存储
 */
export function useLocalStorage<T>(key: string, defaultValue: T) {
  const storedValue = localStorage.getItem(key)
  const initial = storedValue ? JSON.parse(storedValue) : defaultValue
  
  const value = ref<T>(initial)
  
  const setValue = (newValue: T) => {
    value.value = newValue
    localStorage.setItem(key, JSON.stringify(newValue))
  }
  
  const removeValue = () => {
    value.value = defaultValue
    localStorage.removeItem(key)
  }
  
  return {
    value,
    setValue,
    removeValue
  }
}
