// 主题相关样式

// 默认主题变量
:root {
  // 主题颜色变量（将由JavaScript动态设置）
  --theme-primary: #409EFF;
  --theme-primary-light: #ecf5ff;
  --theme-primary-dark: #337ecc;
  --theme-secondary: #909399;
  --theme-background: #ffffff;
  --theme-surface: #f5f7fa;
  --theme-text: #303133;
  --theme-text-secondary: #606266;
  --theme-border: #dcdfe6;
  --theme-success: #67c23a;
  --theme-warning: #e6a23c;
  --theme-error: #f56c6c;
  --theme-info: #909399;
}

// 微软主题
.theme-microsoft {
  --theme-primary: #0078D4;
  --theme-primary-light: #106EBE;
  --theme-primary-dark: #005A9E;
  --theme-secondary: #8764B8;
  --theme-background: #FFFFFF;
  --theme-surface: #F3F2F1;
  --theme-text: #323130;
  --theme-text-secondary: #605E5C;
  --theme-border: #EDEBE9;
  --theme-success: #107C10;
  --theme-warning: #FF8C00;
  --theme-error: #D13438;
  --theme-info: #0078D4;
}

// 苹果主题
.theme-apple {
  --theme-primary: #1D1D1F;
  --theme-primary-light: #86868B;
  --theme-primary-dark: #000000;
  --theme-secondary: #8E8E93;
  --theme-background: #FFFFFF;
  --theme-surface: #F5F5F7;
  --theme-text: #1D1D1F;
  --theme-text-secondary: #86868B;
  --theme-border: #D2D2D7;
  --theme-success: #30D158;
  --theme-warning: #FF9F0A;
  --theme-error: #FF453A;
  --theme-info: #007AFF;
}

// 经典红主题
.theme-classic-red {
  --theme-primary: #dc2626;
  --theme-primary-light: #fca5a5;
  --theme-primary-dark: #991b1b;
}

// 豆沙绿主题
.theme-bean-green {
  --theme-primary: #779A7A;
  --theme-primary-light: #C7EDCC;
  --theme-primary-dark: #2E4031;
  --theme-text: #2E4031;
  --theme-text-secondary: #779A7A;
  --theme-border: #C7EDCC;
  --theme-surface: #f0f9f0;
}

// 柔和蓝主题
.theme-soft-blue {
  --theme-primary: #6E92C2;
  --theme-primary-light: #DDEBF8;
  --theme-primary-dark: #2C3A4A;
  --theme-text: #2C3A4A;
  --theme-text-secondary: #6E92C2;
  --theme-border: #DDEBF8;
  --theme-surface: #f8fafc;
}

// 深色模式
html.dark,
body.dark,
.dark {
  --theme-background: #1a1a1a !important;
  --theme-surface: #2d2d2d !important;
  --theme-text: #ffffff !important;
  --theme-text-secondary: #cccccc !important;
  --theme-border: #404040 !important;
  --theme-success: #67c23a !important;
  --theme-warning: #e6a23c !important;
  --theme-error: #f56c6c !important;
  --theme-info: #909399 !important;

  // Element Plus 深色模式变量
  --el-bg-color: #1a1a1a !important;
  --el-bg-color-page: #2d2d2d !important;
  --el-text-color-primary: #ffffff !important;
  --el-text-color-regular: #cccccc !important;
  --el-text-color-secondary: #909399 !important;
  --el-border-color: #404040 !important;
  --el-border-color-light: #363636 !important;
  --el-border-color-lighter: #2d2d2d !important;
  --el-fill-color: #2d2d2d !important;
  --el-fill-color-light: #363636 !important;
  --el-fill-color-lighter: #404040 !important;

  // 深色模式下的组件样式 - 合并重复样式
  .el-card,
  .el-menu,
  .el-dropdown-menu {
    background-color: var(--theme-surface) !important;
    border-color: var(--theme-border) !important;
  }

  .el-card {
    color: var(--theme-text) !important;
  }

  .el-menu-item {
    color: var(--theme-text-secondary) !important;

    &:hover {
      background-color: var(--theme-border) !important;
      color: var(--theme-text) !important;
    }

    &.is-active {
      background-color: var(--theme-primary-light) !important;
      color: var(--theme-primary) !important;
    }
  }

  .el-dropdown-menu__item {
    color: var(--theme-text) !important;

    &:hover {
      background-color: var(--theme-border) !important;
    }
  }
}

// 主题过渡动画
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

// 使用主题变量的通用样式
.theme-bg-primary { background-color: var(--theme-primary); }
.theme-bg-secondary { background-color: var(--theme-secondary); }
.theme-bg-surface { background-color: var(--theme-surface); }
.theme-bg-background { background-color: var(--theme-background); }

.theme-text-primary { color: var(--theme-text); }
.theme-text-secondary { color: var(--theme-text-secondary); }

.theme-border { border-color: var(--theme-border); }

// 主题适配的组件样式
.el-button--primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);

  &:hover {
    background-color: var(--theme-primary-light);
    border-color: var(--theme-primary-light);
  }

  &:active {
    background-color: var(--theme-primary-dark);
    border-color: var(--theme-primary-dark);
  }
}

// 菜单组件主题适配 - 合并重复样式
.el-menu {
  background-color: var(--theme-background) !important;
  border-color: var(--theme-border) !important;
}

.el-menu-item,
.el-sub-menu__title {
  color: var(--theme-text-secondary) !important;

  &:hover {
    background-color: var(--theme-surface) !important;
    color: var(--theme-text) !important;
  }
}

.el-menu-item.is-active {
  background-color: var(--theme-primary-light) !important;
  color: #ffffff !important;
  border-right: 3px solid var(--theme-primary) !important;
}

// 水平和垂直菜单的特殊样式
.el-menu--horizontal .el-menu-item.is-active {
  border-bottom-color: var(--theme-primary);
  color: var(--theme-primary);
  border-right: none;
}

.el-menu--vertical .el-menu-item.is-active {
  background-color: var(--theme-primary-light);
  color: #ffffff;
}
