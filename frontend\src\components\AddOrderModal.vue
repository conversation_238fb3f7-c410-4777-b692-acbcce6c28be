<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增订单"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <!-- 地址解析区域 -->
    <div class="parser-input-group">
      <el-input
        v-model="addressText"
        type="textarea"
        :rows="3"
        placeholder="请粘贴完整地址信息，格式：收件人，电话，省市区街道详细地址 订单号"
        class="address-textarea"
      />
      <el-button
        type="primary"
        @click="handleSplitAddress"
        :disabled="!addressText.trim()"
        class="split-button"
      >
        分拆填充
      </el-button>
    </div>

    <!-- 确定按钮区域 -->
    <div class="confirm-button-section">
      <el-checkbox v-model="isReuse" label="复用" style="margin-right: 8px" />
      <el-checkbox v-model="isCombine" label="合单" style="margin-right: 12px" />
      <el-button type="primary" @click="handleSaveOrder" class="confirm-button unified-button">
        确定
      </el-button>
    </div>

    <!-- 订单信息表单 -->
    <el-form
      ref="formRef"
      :model="addForm"
      :rules="formRules"
      label-width="100px"
      class="order-form"
    >

      <!-- 第一行：订单号|平台 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单号" prop="order_number">
            <el-input v-model="addForm.order_number" placeholder="请输入订单号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="平台" prop="platform_id">
            <el-select v-model="addForm.platform_id" placeholder="请选择平台" style="width: 100%">
              <el-option label="淘宝" value="taobao" />
              <el-option label="天猫" value="tmall" />
              <el-option label="京东" value="jd" />
              <el-option label="拼多多" value="pdd" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：店铺|数量 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="店铺" prop="shop_id">
            <el-select v-model="addForm.shop_id" placeholder="请选择店铺" style="width: 100%">
              <el-option label="旗舰店" value="flagship" />
              <el-option label="专营店" value="franchise" />
              <el-option label="专卖店" value="exclusive" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number
              v-model="addForm.quantity"
              :min="1"
              :max="9999"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：货号|销售金额|单价 -->
      <el-row :gutter="10" class="amount-row">
        <el-col :span="10">
          <el-form-item label="商品货号" prop="product_code">
            <el-input v-model="addForm.product_code" placeholder="请输入商品货号" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="销售金额">
            <el-input
              v-model="addForm.sale_price"
              type="number"
              placeholder="0.00"
              step="0.01"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="单价">
            <el-input
              v-model="addForm.unit_price"
              type="number"
              placeholder="0.00"
              step="0.01"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：尺码|结算金额|总价 -->
      <el-row :gutter="10" class="amount-row">
        <el-col :span="10">
          <el-form-item label="尺码" prop="size">
            <el-input v-model="addForm.size" placeholder="请输入尺码" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="结算金额">
            <el-input
              v-model="addForm.settlement_amount"
              type="number"
              placeholder="0.00"
              step="0.01"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="总价">
            <el-input
              v-model="addForm.total_amount"
              type="number"
              placeholder="0.00"
              step="0.01"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 收货地址 -->
      <el-row :gutter="10" class="address-row">
        <el-col :span="8">
          <el-form-item label="收件人" prop="receiver_name">
            <el-input v-model="addForm.receiver_name" placeholder="请输入收件人姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收件人电话" prop="receiver_phone">
            <el-input v-model="addForm.receiver_phone" placeholder="请输入收件人电话" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="省" prop="receiver_province">
            <el-input v-model="addForm.receiver_province" placeholder="请输入省份" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="address-row">
        <el-col :span="8">
          <el-form-item label="市" prop="receiver_city">
            <el-input v-model="addForm.receiver_city" placeholder="请输入城市" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="区（县）" prop="receiver_district">
            <el-input v-model="addForm.receiver_district" placeholder="请输入区县" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="街道">
            <el-input v-model="addForm.receiver_street" placeholder="请输入街道" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="详细地址" prop="receiver_address">
            <el-input
              v-model="addForm.receiver_address"
              type="textarea"
              :rows="2"
              placeholder="请输入详细地址"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 时间信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="下单时间" prop="order_time">
            <el-date-picker
              v-model="addForm.order_time"
              type="datetime"
              placeholder="选择下单时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付时间" prop="paid_time">
            <el-date-picker
              v-model="addForm.paid_time"
              type="datetime"
              placeholder="选择支付时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="买家备注">
            <el-input
              v-model="addForm.buyer_note"
              type="textarea"
              :rows="3"
              placeholder="请输入买家备注"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="内部备注">
            <el-input
              v-model="addForm.internal_note"
              type="textarea"
              :rows="3"
              placeholder="请输入内部备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="unified-button">取消</el-button>
        <el-button type="primary" @click="submitForm" class="unified-button">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// 互斥复选框状态
const isReuse = ref(false)
const isCombine = ref(false)

// 互斥逻辑
watch(isReuse, (newValue) => {
  if (newValue) {
    isCombine.value = false
  }
})

watch(isCombine, (newValue) => {
  if (newValue) {
    isReuse.value = false
  }
})


// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm-add'])

// 响应式数据
const formRef = ref()
const addressText = ref('')

// 计算属性：控制对话框显示
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取当前时间的函数
const getCurrentDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 表单数据
const addForm = reactive({
  // 订单基本信息
  order_number: '',
  platform_id: '',
  shop_id: '',

  // 商品信息
  product_code: '',
  size: '',
  quantity: 1,

  // 金额信息
  unit_price: '',
  total_amount: '',
  sale_price: '',
  settlement_amount: '',

  // 收货地址
  receiver_name: '',
  receiver_phone: '',
  receiver_province: '',
  receiver_city: '',
  receiver_district: '',
  receiver_street: '',
  receiver_address: '',

  // 时间信息
  order_time: getCurrentDateTime(),
  paid_time: getCurrentDateTime(),

  // 备注信息
  buyer_note: '',
  internal_note: ''
})

// 表单验证规则
const formRules = {
  order_number: [
    { required: true, message: '请输入订单号', trigger: 'blur' }
  ],
  platform_id: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  shop_id: [
    { required: true, message: '请选择店铺', trigger: 'change' }
  ],
  product_code: [
    { required: true, message: '请输入商品货号', trigger: 'blur' }
  ],
  size: [
    { required: true, message: '请输入尺码', trigger: 'blur' }
  ],
  receiver_name: [
    { required: true, message: '请输入收件人姓名', trigger: 'blur' }
  ],
  receiver_phone: [
    { required: true, message: '请输入收件人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  receiver_province: [
    { required: true, message: '请输入省份', trigger: 'blur' }
  ],
  receiver_city: [
    { required: true, message: '请输入城市', trigger: 'blur' }
  ],
  receiver_district: [
    { required: true, message: '请输入区县', trigger: 'blur' }
  ],
  receiver_address: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  order_time: [
    { required: true, message: '请选择下单时间', trigger: 'change' }
  ],
  paid_time: [
    { required: true, message: '请选择支付时间', trigger: 'change' }
  ]
}

// 智能地址解析函数
const handleSplitAddress = () => {
  if (!addressText.value.trim()) {
    ElMessage.warning('请先输入地址信息')
    return
  }

  const text = addressText.value.trim()
  let parsed = false

  // 规则1: 空格分隔的完整订单信息
  const rule1Regex = /^(\S+)\s+(\d+)\s+([^\s]+)\s+(\d+)\s+(\S+)\s+(\S+)\s+(\d+)\s+(\d+\.?\d*)\s+(\d+\.?\d*)\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+([^\s]+)\s+(.+)$/
  const rule1Match = text.match(rule1Regex)

  if (rule1Match) {
    // 解析完整订单信息
    addForm.receiver_name = rule1Match[1]
    addForm.receiver_phone = rule1Match[2]
    parseAddress(rule1Match[3])
    addForm.order_number = rule1Match[4]
    addForm.product_code = rule1Match[5]
    addForm.size = rule1Match[6]
    addForm.quantity = parseInt(rule1Match[7])
    addForm.unit_price = rule1Match[8]
    addForm.settlement_amount = rule1Match[9]
    addForm.order_time = rule1Match[10]
    addForm.paid_time = rule1Match[11]
    addForm.platform_id = rule1Match[12]
    addForm.shop_id = rule1Match[13]
    parsed = true
  } else {
    // 规则2: 逗号分隔的基本信息
    const rule2Regex = /^(.+)，(\d+)，(.+)\s*(\d+)?$/
    const rule2Match = text.match(rule2Regex)

    if (rule2Match) {
      addForm.receiver_name = rule2Match[1]
      addForm.receiver_phone = rule2Match[2]
      parseAddress(rule2Match[3])
      if (rule2Match[4]) {
        addForm.order_number = rule2Match[4]
      }
      parsed = true
    } else {
      // 规则3: 备用逗号分隔
      const parts = text.split('，')
      if (parts.length >= 3) {
        addForm.receiver_name = parts[0]
        addForm.receiver_phone = parts[1]
        const addressPart = parts.slice(2).join('，')
        parseAddress(addressPart)
        parsed = true
      }
    }
  }

  if (parsed) {
    ElMessage.success('地址解析成功！')
    // 清空地址文本框
    addressText.value = ''
  } else {
    ElMessage.error('地址格式不正确，请检查后重试')
  }
}

// 解析地址的辅助函数
const parseAddress = (addressStr) => {
  // 简单的地址解析逻辑，可以根据实际需求优化
  const address = addressStr.trim()

  // 尝试提取省市区信息
  const provinceRegex = /(.*?省|.*?自治区|.*?市)/
  const cityRegex = /(.*?市|.*?地区|.*?州)/
  const districtRegex = /(.*?区|.*?县|.*?市)/

  let remainingAddress = address

  // 提取省份
  const provinceMatch = remainingAddress.match(provinceRegex)
  if (provinceMatch) {
    addForm.receiver_province = provinceMatch[1]
    remainingAddress = remainingAddress.replace(provinceMatch[1], '')
  }

  // 提取城市
  const cityMatch = remainingAddress.match(cityRegex)
  if (cityMatch) {
    addForm.receiver_city = cityMatch[1]
    remainingAddress = remainingAddress.replace(cityMatch[1], '')
  }

  // 提取区县
  const districtMatch = remainingAddress.match(districtRegex)
  if (districtMatch) {
    addForm.receiver_district = districtMatch[1]
    remainingAddress = remainingAddress.replace(districtMatch[1], '')
  }

  // 剩余部分作为详细地址
  addForm.receiver_address = remainingAddress.trim()
}

// 地址解析确认
const handleParseConfirm = () => {
  ElMessage.success('地址解析确认完成！')
  // 可以在这里添加其他确认逻辑，比如：
  // - 验证解析结果
  // - 清空地址解析输入框
  // - 聚焦到下一个需要填写的字段
  addressText.value = ''
}

// 保存订单
const handleSaveOrder = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 这里可以添加保存订单的逻辑
    console.log('保存订单数据:', addForm)

    ElMessage.success('订单保存成功！')

    // 保存成功后关闭对话框
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.assign(addForm, {
    // 订单基本信息
    order_number: '',
    platform_id: '',
    shop_id: '',

    // 商品信息
    product_code: '',
    size: '',
    quantity: 1,

    // 金额信息
    unit_price: '',
    total_amount: '',
    sale_price: '',
    settlement_amount: '',

    // 收货地址
    receiver_name: '',
    receiver_phone: '',
    receiver_province: '',
    receiver_city: '',
    receiver_district: '',
    receiver_street: '',
    receiver_address: '',

    // 时间信息
    order_time: getCurrentDateTime(),
    paid_time: getCurrentDateTime(),

    // 备注信息
    buyer_note: '',
    internal_note: ''
  })

  // 清空地址解析文本框
  addressText.value = ''

  // 清除表单验证状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    // 执行表单验证
    await formRef.value.validate()

    // 验证通过，触发确认事件
    emit('confirm-add', { ...addForm })

    // 显示成功消息
    ElMessage.success('订单添加成功！')

    // 关闭对话框并重置表单
    handleClose()
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整')
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听对话框显示状态，重置时间
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 对话框打开时，重置时间为当前时间
    addForm.order_time = getCurrentDateTime()
    addForm.paid_time = getCurrentDateTime()
  }
})
</script>

<style scoped>
.parser-input-group {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  margin-bottom: 8px;
}

.address-textarea {
  flex: 1;
}

.split-button {
  flex-shrink: 0;
  height: 76px; /* 匹配textarea高度 */
}

/* 强制分拆填充按钮应用主题色 */
.split-button.el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: var(--el-color-white);
}

/* 表单区域样式 */
.order-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}

/* 金额输入框行样式 - 减少间距让输入框更宽 */
.amount-row .el-col {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

/* 地址输入框行样式 - 减少间距让输入框更宽 */
.address-row .el-col {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

/* 确定按钮区域样式 */
.confirm-button-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: 8px;
}

/* 统一按钮样式 */
.unified-button {
  width: 88px !important;
  height: 32px !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.confirm-button {
  padding: 8px 24px;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .parser-input-group {
    flex-direction: column;
  }

  .split-button {
    height: auto;
    width: 100%;
  }

  .order-form {
    max-height: 50vh;
  }
}

/* 滚动条样式 */
.order-form::-webkit-scrollbar {
  width: 6px;
}

.order-form::-webkit-scrollbar-track {
  background: var(--theme-surface, #f5f7fa);
  border-radius: 3px;
}

.order-form::-webkit-scrollbar-thumb {
  background: var(--theme-border, #e4e7ed);
  border-radius: 3px;
}

.order-form::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-secondary, #909399);
}

/* Element Plus 组件样式调整 */
:deep(.el-form-item__label) {
  color: var(--theme-text, #303133);
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-color: var(--theme-border, #e4e7ed);
}

:deep(.el-input__wrapper:hover) {
  border-color: var(--theme-primary, #409eff);
}

:deep(.el-select .el-input__wrapper) {
  border-color: var(--theme-border, #e4e7ed);
}

:deep(.el-date-editor .el-input__wrapper) {
  border-color: var(--theme-border, #e4e7ed);
}

:deep(.el-textarea__inner) {
  border-color: var(--theme-border, #e4e7ed);
}

:deep(.el-textarea__inner:hover) {
  border-color: var(--theme-primary, #409eff);
}
</style>
