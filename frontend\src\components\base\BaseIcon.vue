<template>
  <el-icon
    :size="size"
    :color="color"
    :class="iconClass"
    @click="handleClick"
  >
    <component :is="iconComponent" />
  </el-icon>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icons, type IconName } from '@/utils/icons'

interface Props {
  name: IconName
  size?: string | number
  color?: string
  clickable?: boolean
  disabled?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 16,
  clickable: false,
  disabled: false,
  loading: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const iconComponent = computed(() => {
  return Icons[props.name]
})

const iconClass = computed(() => {
  return {
    'base-icon': true,
    'base-icon--clickable': props.clickable,
    'base-icon--disabled': props.disabled,
    'base-icon--loading': props.loading
  }
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped lang="scss">
.base-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &--clickable {
    cursor: pointer;
    transition: $transition-base;
    
    &:hover {
      opacity: 0.8;
      transform: scale(1.1);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      opacity: 0.5;
      transform: none;
    }
  }
  
  &--loading {
    animation: rotate 1s linear infinite;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
