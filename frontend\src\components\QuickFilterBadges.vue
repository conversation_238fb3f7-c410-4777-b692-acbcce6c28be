<template>
  <div class="quick-filter-badges">
    <!-- 快筛徽章 - 一行显示 -->
    <div class="tabs-vertical-badge">
      <!-- 状态筛选徽章 -->
      <li
        v-for="filter in statusFilters"
        :key="filter.id"
        :class="{ active: filter.id === activeStatusFilter }"
        @click="selectStatusFilter(filter.id)"
      >
        <a href="#" @click.prevent>
          {{ filter.name }}
          <span class="badge">{{ filter.count }}</span>
        </a>
      </li>

      <!-- 时间筛选徽章 -->
      <li
        v-for="filter in timeFilters"
        :key="filter.id"
        :class="{ active: filter.id === activeTimeFilter }"
        @click="selectTimeFilter(filter.id)"
      >
        <a href="#" @click.prevent>
          {{ filter.name }}
          <span class="badge">{{ filter.count }}</span>
        </a>
      </li>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// Define emits for component communication
const emit = defineEmits(['status-filter-change', 'time-filter-change'])

// 状态筛选器 - 与订单数据中的状态保持一致
const statusFilters = ref([
  { id: 'all', name: '全部', count: 0 },
  { id: 'pending_review', name: '待审核', count: 0 },
  { id: 'reviewed', name: '已审核', count: 0 },
  { id: 'submitted', name: '已交单', count: 0 },
  { id: 'exported', name: '已导出', count: 0 },
  { id: 'picking', name: '配货中', count: 0 },
  { id: 'pending_shipping', name: '待发货', count: 0 },
  { id: 'shipped', name: '已发货', count: 0 },
  { id: 'completed', name: '已完成', count: 0 },
  { id: 'closed', name: '已关闭', count: 0 },
  { id: 'after_sales', name: '售后中', count: 0 },
])

const timeFilters = ref([
  { id: 'countdown_half_h', name: '倒计半H', count: 0 },
  { id: 'countdown_1h', name: '倒计1H', count: 0 },
  { id: 'countdown_2h', name: '倒计2H', count: 0 },
  { id: 'countdown_4h', name: '倒计4H', count: 0 },
  { id: 'countdown_8h', name: '倒计8H', count: 0 },
  { id: 'overdue', name: '已超时', count: 0 },
  { id: 'virtual_shipping_24h', name: '虚拟发货24H', count: 0 },
])

// 统一的激活状态管理 - 只能选择一个徽章
const activeFilter = ref('all')

const selectStatusFilter = (id: string) => {
  activeFilter.value = id
  emit('status-filter-change', id)
}

const selectTimeFilter = (id: string) => {
  activeFilter.value = id
  emit('time-filter-change', id)
}

// 兼容性属性，用于模板中的判断
const activeStatusFilter = computed(() => {
  const statusIds = statusFilters.value.map(f => f.id)
  return statusIds.includes(activeFilter.value) ? activeFilter.value : ''
})

const activeTimeFilter = computed(() => {
  const timeIds = timeFilters.value.map(f => f.id)
  return timeIds.includes(activeFilter.value) ? activeFilter.value : ''
})

// 键盘导航功能 - 支持所有徽章
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
    event.preventDefault()

    // 合并所有筛选器
    const allFilters = [...statusFilters.value, ...timeFilters.value]
    const currentIndex = allFilters.findIndex(filter => filter.id === activeFilter.value)
    let newIndex = currentIndex

    if (event.key === 'ArrowLeft') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : allFilters.length - 1
    } else if (event.key === 'ArrowRight') {
      newIndex = currentIndex < allFilters.length - 1 ? currentIndex + 1 : 0
    }

    const selectedFilter = allFilters[newIndex]
    // 判断是状态筛选器还是时间筛选器
    if (statusFilters.value.some(f => f.id === selectedFilter.id)) {
      selectStatusFilter(selectedFilter.id)
    } else {
      selectTimeFilter(selectedFilter.id)
    }
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 动态更新徽章数量
const updateBadgeCount = (filterType: string, count: number, isTimeFilter = false) => {
  const filters = isTimeFilter ? timeFilters.value : statusFilters.value
  const filter = filters.find(f => f.id === filterType)
  if (filter) {
    filter.count = count
  }
}

// 暴露方法给父组件使用
defineExpose({
  updateBadgeCount
})
</script>

<style scoped>
/* CSS变量定义 - 优先使用主题变量 */
:root {
  /* 主色系 - 引用主题颜色 */
  --primary-color: var(--el-color-primary, #dc2626);
  --primary-hover: var(--el-color-primary-dark-2, #b91c1c);

  /* 文本色系 */
  --text-primary: #374151;
  --text-secondary: #6b7280;
  --text-white: #ffffff;

  /* 背景色系 */
  --bg-white: #ffffff;
  --bg-gray-50: #f9fafb;

  /* 边框色系 */
  --border-color: #e5e7eb;

  /* 圆角系统 */
  --radius-full: 9999px;

  /* 过渡动画 */
  --transition-slow: 0.3s ease;
}

.quick-filter-badges {
  background-color: var(--bg-white);
  padding: 0;
  margin: 0;
}

/* ===== 订单列表快筛徽章样式 ===== */

/* 徽章容器 */
.tabs-vertical-badge {
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
  flex-wrap: wrap;
  gap: 0;
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  z-index: 50;
  height: 40px;
}

/* 徽章项目 */
.tabs-vertical-badge li {
  position: relative;
  margin: 0;
  padding: 0;
}

/* 徽章链接 */
.tabs-vertical-badge li a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  width: 100px;
  height: 40px;
  padding: 0 12px;
  margin: 0;
  color: var(--text-secondary);
  background-color: transparent;
  text-decoration: none;
  transition: all var(--transition-slow);
  border: none;
  border-bottom: 1px solid transparent;
  position: relative;
  font-weight: 500;
  font-size: 13px;
  box-sizing: border-box;
  z-index: 100;
}

/* 底线指示器 */
.tabs-vertical-badge li a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: transparent;
  transition: all var(--transition-slow);
}

/* 数字徽章 - 圆形设计，非激活状态使用主题主颜色 */
.tabs-vertical-badge .badge {
  background-color: var(--el-color-primary, #409eff);  /* 非激活状态使用主题主颜色 */
  color: #ffffff;                      /* 白色字体 */
  font-size: 10px;
  font-weight: 600;
  padding: 0;                          /* 圆形徽章不需要内边距 */
  border-radius: 50%;                  /* 完全圆形 */
  transition: var(--badge-transition, all 0.2s ease);
  width: 18px;                         /* 固定宽度，确保圆形 */
  height: 18px;                        /* 固定高度，确保圆形 */
  min-width: 18px;
  text-align: center;
  line-height: 18px;                   /* 行高等于高度，垂直居中 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

/* 激活状态和悬停效果 - 使用更强的选择器 */
.tabs-vertical-badge li.active a,
.tabs-vertical-badge li a:hover {
  background-color: var(--bg-gray-50) !important;
  color: var(--el-color-primary, #409eff) !important;
  border-bottom: 1px solid var(--el-color-primary, #409eff) !important;
}

/* 激活状态的底线指示器 - 使用更强的选择器 */
.tabs-vertical-badge li.active a::before,
.tabs-vertical-badge li a:hover::before {
  background-color: var(--el-color-primary, #409eff) !important;
  height: 1px !important;
}

/* 悬停状态下的数字徽章 - 保持主题色（非激活状态） */
.tabs-vertical-badge li:not(.active) a:hover .badge {
  background-color: var(--el-color-primary, #409eff) !important;
  color: #ffffff !important;
  transform: scale(1.05);                 /* 微妙的放大效果 */
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

/* 激活状态下的数字徽章 - 使用经典红色，最高优先级 */
.tabs-vertical-badge li.active .badge,
.tabs-vertical-badge li.active a:hover .badge {
  background-color: #e53e3e !important;  /* 经典红色 - 更鲜艳的红色 */
  color: #ffffff !important;              /* 白色字体 */
  box-shadow: 0 1px 3px rgba(229, 62, 62, 0.4) !important;
  transform: none !important;             /* 激活状态不放大 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-vertical-badge {
    flex-direction: column;
    gap: 0;
    height: auto;
  }

  .tabs-vertical-badge li {
    margin: 0;
    width: 100%;
  }

  .tabs-vertical-badge li a {
    width: 100%;
    justify-content: flex-start;
    height: 40px;
    padding: 0 16px;
    border: 0;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
  }

  .tabs-vertical-badge li:last-child a {
    border-bottom: 0;
  }
}
</style>