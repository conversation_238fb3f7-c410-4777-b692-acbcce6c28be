<template>
  <el-select
    :model-value="modelValue"
    :placeholder="placeholder"
    :clearable="clearable"
    filterable
    style="width: 100%"
    @update:modelValue="val => emit('update:modelValue', val)"
    @change="val => emit('change', val)"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script setup lang="ts">
import { PropType } from 'vue'

interface Option {
  value: string | number;
  label: string;
}

defineProps({
  modelValue: { type: [String, Number], default: '' },
  options: { type: Array as PropType<Option[]>, required: true },
  placeholder: { type: String, default: '请选择' },
  clearable: { type: Boolean, default: false }
})

const emit = defineEmits(['update:modelValue', 'change'])
</script> 