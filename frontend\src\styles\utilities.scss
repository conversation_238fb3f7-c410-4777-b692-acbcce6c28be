// ===========================================
// 工具类 - Utility Classes
// ===========================================

// 导入变量
@import './variables.scss';

// ===========================================
// 布局工具类 - Layout Utilities
// ===========================================

// Flexbox 工具类
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

// 对齐工具类
.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.items-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

// 常用组合类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// ===========================================
// 间距工具类 - Spacing Utilities
// ===========================================

// Margin 工具类
.m-0 { margin: 0; }
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }
.m-xxl { margin: $spacing-xxl; }

// Margin Top
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }
.mt-xxl { margin-top: $spacing-xxl; }

// Margin Bottom
.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }
.mb-xxl { margin-bottom: $spacing-xxl; }

// Margin Left
.ml-0 { margin-left: 0; }
.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }
.ml-xxl { margin-left: $spacing-xxl; }

// Margin Right
.mr-0 { margin-right: 0; }
.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }
.mr-xxl { margin-right: $spacing-xxl; }

// Padding 工具类
.p-0 { padding: 0; }
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }
.p-xxl { padding: $spacing-xxl; }

// Padding Top
.pt-0 { padding-top: 0; }
.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }
.pt-xxl { padding-top: $spacing-xxl; }

// Padding Bottom
.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }
.pb-xxl { padding-bottom: $spacing-xxl; }

// Padding Left
.pl-0 { padding-left: 0; }
.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }
.pl-xxl { padding-left: $spacing-xxl; }

// Padding Right
.pr-0 { padding-right: 0; }
.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }
.pr-xxl { padding-right: $spacing-xxl; }

// ===========================================
// 文本工具类 - Text Utilities
// ===========================================

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-xs { font-size: $font-size-extra-small; }
.text-sm { font-size: $font-size-small; }
.text-base { font-size: $font-size-base; }
.text-md { font-size: $font-size-medium; }
.text-lg { font-size: $font-size-large; }

.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-bold { font-weight: $font-weight-bold; }

.text-primary { color: $text-primary; }
.text-regular { color: $text-regular; }
.text-secondary { color: $text-secondary; }
.text-placeholder { color: $text-placeholder; }
.text-white { color: $text-white; }

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-all;
  word-wrap: break-word;
}

// ===========================================
// 显示工具类 - Display Utilities
// ===========================================

.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.visible { visibility: visible; }
.invisible { visibility: hidden; }

// ===========================================
// 位置工具类 - Position Utilities
// ===========================================

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// ===========================================
// 边框工具类 - Border Utilities
// ===========================================

.border-0 { border: none; }
.border { border: 1px solid $border-base; }
.border-light { border: 1px solid $border-light; }
.border-lighter { border: 1px solid $border-lighter; }

.border-t { border-top: 1px solid $border-base; }
.border-b { border-bottom: 1px solid $border-base; }
.border-l { border-left: 1px solid $border-base; }
.border-r { border-right: 1px solid $border-base; }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: $border-radius-small; }
.rounded { border-radius: $border-radius-base; }
.rounded-lg { border-radius: $border-radius-round; }
.rounded-full { border-radius: $border-radius-circle; }

// ===========================================
// 背景工具类 - Background Utilities
// ===========================================

.bg-white { background-color: $bg-white; }
.bg-page { background-color: $bg-page; }
.bg-light { background-color: $bg-light; }
.bg-lighter { background-color: $bg-lighter; }

.bg-primary { background-color: $primary-color; }
.bg-success { background-color: $success-color; }
.bg-warning { background-color: $warning-color; }
.bg-danger { background-color: $danger-color; }
.bg-info { background-color: $info-color; }

// ===========================================
// 阴影工具类 - Shadow Utilities
// ===========================================

.shadow-none { box-shadow: none; }
.shadow-light { box-shadow: $box-shadow-light; }
.shadow { box-shadow: $box-shadow-base; }
.shadow-dark { box-shadow: $box-shadow-dark; }

// ===========================================
// 过渡工具类 - Transition Utilities
// ===========================================

.transition { transition: $transition-base; }
.transition-fade { transition: $transition-fade; }
.transition-md-fade { transition: $transition-md-fade; }

// ===========================================
// 容器工具类 - Container Utilities
// ===========================================

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-lg;
  
  @media (min-width: $breakpoint-sm) {
    max-width: 720px;
  }
  
  @media (min-width: $breakpoint-md) {
    max-width: 960px;
  }
  
  @media (min-width: $breakpoint-lg) {
    max-width: 1140px;
  }
  
  @media (min-width: $breakpoint-xl) {
    max-width: 1320px;
  }
}

.container-fluid {
  width: 100%;
  padding: 0 $spacing-lg;
}

// ===========================================
// 零间距工具类 - Zero Spacing Utilities
// ===========================================

.no-margin {
  margin: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

.no-border {
  border: none !important;
}

.no-shadow {
  box-shadow: none !important;
}

.no-radius {
  border-radius: 0 !important;
}

// 组合零间距类
.no-spacing {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

.reset-card {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}
