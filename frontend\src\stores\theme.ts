import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import type { Theme, ThemeColors, ThemeType } from '@/types/theme'
import { presetThemes, getTheme, createCustomTheme } from '@/config/themes'
import { getStorage, setStorage } from '@/utils/storage'

// 有效主题列表
const VALID_THEMES: ThemeType[] = ['microsoft', 'apple', 'classic-red', 'bean-green', 'soft-blue', 'custom']

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const currentThemeId = ref<ThemeType>('microsoft')
  const customColors = ref<Partial<ThemeColors>>({})
  const isDarkMode = ref(false)

  // 计算属性
  const currentTheme = computed((): Theme => {
    if (currentThemeId.value === 'custom') {
      return createCustomTheme(customColors.value)
    }
    return getTheme(currentThemeId.value)
  })

  const themeColors = computed(() => currentTheme.value.colors)

  // 方法
  const setTheme = (themeId: ThemeType) => {
    // 确保 themeId 是有效的字符串
    if (typeof themeId === 'string' && VALID_THEMES.includes(themeId)) {
      currentThemeId.value = themeId
      applyTheme()
      saveThemeConfig()
    }
  }

  const setCustomColors = (colors: Partial<ThemeColors>) => {
    customColors.value = { ...customColors.value, ...colors }
    if (currentThemeId.value === 'custom') {
      applyTheme()
    }
    saveThemeConfig()
  }

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    applyTheme()
    saveThemeConfig()
  }

  const applyTheme = () => {
    const root = document.documentElement
    const colors = currentTheme.value.colors

    // 应用CSS变量
    root.style.setProperty('--theme-primary', colors.primary)
    root.style.setProperty('--theme-primary-light', colors.primaryLight)
    root.style.setProperty('--theme-primary-dark', colors.primaryDark)
    root.style.setProperty('--theme-secondary', colors.secondary)
    root.style.setProperty('--theme-background', colors.background)
    root.style.setProperty('--theme-surface', colors.surface)
    root.style.setProperty('--theme-text', colors.text)
    root.style.setProperty('--theme-text-secondary', colors.textSecondary)
    root.style.setProperty('--theme-border', colors.border)
    root.style.setProperty('--theme-success', colors.success)
    root.style.setProperty('--theme-warning', colors.warning)
    root.style.setProperty('--theme-error', colors.error)
    root.style.setProperty('--theme-info', colors.info)

    // 应用Element Plus主题变量
    root.style.setProperty('--el-color-primary', colors.primary)
    root.style.setProperty('--el-color-primary-light-3', colors.primaryLight)
    root.style.setProperty('--el-color-primary-dark-2', colors.primaryDark)
    root.style.setProperty('--el-color-success', colors.success)
    root.style.setProperty('--el-color-warning', colors.warning)
    root.style.setProperty('--el-color-danger', colors.error)
    root.style.setProperty('--el-color-info', colors.info)
    root.style.setProperty('--el-bg-color', colors.background)
    root.style.setProperty('--el-bg-color-page', colors.surface)
    root.style.setProperty('--el-text-color-primary', colors.text)
    root.style.setProperty('--el-text-color-regular', colors.textSecondary)
    root.style.setProperty('--el-border-color', colors.border)

    // 设置主题类名
    root.className = root.className.replace(/theme-\w+/g, '')
    const themeId = typeof currentThemeId.value === 'string' ? currentThemeId.value : 'microsoft'
    root.classList.add(`theme-${themeId}`)
    
    if (isDarkMode.value) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }

  const saveThemeConfig = () => {
    const config = {
      currentTheme: currentThemeId.value,
      customColors: customColors.value,
      isDarkMode: isDarkMode.value
    }
    setStorage('theme-config', config)
  }

  const loadThemeConfig = () => {
    const config = getStorage('theme-config')
    if (config && typeof config === 'object') {
      // 确保 currentTheme 是有效的字符串
      const themeId = config.currentTheme
      currentThemeId.value = (typeof themeId === 'string' && VALID_THEMES.includes(themeId))
        ? themeId as ThemeType
        : 'microsoft'

      customColors.value = (config.customColors && typeof config.customColors === 'object')
        ? config.customColors
        : {}

      isDarkMode.value = typeof config.isDarkMode === 'boolean'
        ? config.isDarkMode
        : false
    }
    applyTheme()
  }

  const resetTheme = () => {
    currentThemeId.value = 'microsoft'
    customColors.value = {}
    isDarkMode.value = false
    applyTheme()
    saveThemeConfig()
  }

  const getPresetThemes = () => {
    return Object.values(presetThemes)
  }

  // 初始化
  const init = () => {
    loadThemeConfig()
  }

  // 监听主题变化
  watch(currentThemeId, () => {
    applyTheme()
  })

  return {
    // 状态
    currentThemeId,
    customColors,
    isDarkMode,
    
    // 计算属性
    currentTheme,
    themeColors,
    
    // 方法
    setTheme,
    setCustomColors,
    toggleDarkMode,
    applyTheme,
    resetTheme,
    getPresetThemes,
    init
  }
})
