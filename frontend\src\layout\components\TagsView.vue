<template>
  <div class="tab-navigation">
    <div class="tab-list">
      <a
        v-for="tab in visitedViews"
        :key="tab.path"
        :class="['tab-item', { active: isActive(tab) }]"
        @click.prevent="clickTab(tab)"
      >
        <span class="tab-text">{{ tab.title }}</span>
        <button v-if="!isAffix(tab)" class="tab-close" @click.stop.prevent="closeTab(tab)">×</button>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTagsViewStore, TagView } from '@/stores/tagsView'

const store = useTagsViewStore()
const route = useRoute()
const router = useRouter()

const visitedViews = computed(() => store.visitedViews)

watch(
  () => route.path,
  () => {
    addTags()
  }
)

onMounted(() => {
  // 初始化固定标签（首页）
  store.initAffixTags()
  // 添加当前页面标签
  addTags()
})

function addTags() {
  const { name } = route
  if (name) {
    store.addView(route)
  }
}

function closeTab(view: TagView) {
  store.delView(view).then((result: any) => {
    if (isActive(view)) {
      toLastView(result.visitedViews, view)
    }
  })
}

function clickTab(tab: TagView) {
  router.push(tab.path!)
}

function isActive(tab: TagView) {
  return tab.path === route.path
}

function toLastView(visitedViews: TagView[], view: TagView) {
  const latestView = visitedViews.slice(-1)[0]
  if (latestView) {
    router.push(latestView.path!)
  } else {
    // 如果是首页，则重定向到首页，否则跳转到根目录
    if (view.name === 'Home') {
      router.replace({ path: '/redirect' + view.path })
    } else {
      router.push('/')
    }
  }
}

function isAffix(tab: TagView) {
  return tab.meta && tab.meta.affix
}
</script>

<style scoped>
/* ===== 页面标签栏样式 - 斜角切割风格 ===== */

/* 标签栏容器 - 限定40px高度，边距为0 */
.tab-navigation {
  height: 40px;
  background-color: var(--theme-background, #ffffff);
  border-bottom: 1px solid var(--theme-border, #e4e7ed);
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  flex-shrink: 0;
  overflow-x: auto;
}

/* 标签列表 - 斜角切割风格 */
.tab-list {
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
  height: 40px;
  align-items: center;
}

.tab-list .tab-item {
  position: relative;
  margin-left: 0px;
}

.tab-list .tab-item:first-child {
  margin-left: 5px;
}

/* 标签项基础样式 */
.tab-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  height: 40px;
  color: var(--theme-text, #303133);
  background-color: var(--theme-surface, #f5f7fa);
  text-decoration: none;
  transform: skewX(-15deg);
  transition: all 0.3s ease;
  border-top: 0;
  border-bottom: 2px solid var(--theme-primary, #409EFF);
  border-left: 0;
  border-right: 3px solid var(--theme-border, #e4e7ed);
  position: relative;
  font-size: 12px;
  font-weight: 500;
  z-index: 1;
  box-sizing: border-box;
  cursor: pointer;
}

/* 标签文字反向倾斜 */
.tab-item .tab-text {
  display: inline-block;
  transform: skewX(15deg);
  white-space: nowrap;
}

/* 激活状态和悬停效果 */
.tab-item.active,
.tab-item:hover {
  background-color: var(--theme-background, #ffffff);
  color: var(--theme-primary, #409EFF);
  border-top: 0;
  border-bottom: 2px solid var(--theme-primary, #409EFF);
  border-left: 0;
  border-right: 3px solid var(--theme-primary, #409EFF);
  transform: translateY(-3px) skewX(-15deg);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* 关闭按钮样式 */
.tab-close {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  color: inherit;
  cursor: pointer;
  font-size: 18px !important;
  font-weight: normal !important;
  font-family: inherit;
  padding: 0 !important;
  margin: 0 0 0 6px;
  opacity: 0.7;
  transform: skewX(15deg);
  line-height: 1;
  width: auto;
  height: auto;
  display: inline-block;
  vertical-align: middle;
}

.tab-close:hover {
  opacity: 1;
  color: var(--theme-error, #f56c6c);
  background: transparent !important;
  transform: skewX(15deg) scale(1.1);
}
</style> 