# 📋 订单列表快筛徽章 - AI开发指令

## 🎯 功能描述
订单列表页面的快筛徽章组件，用于快速筛选不同状态的订单，具有数字徽章显示和激活状态切换功能。

## 🎨 视觉效果
- 水平排列的筛选标签
- 每个标签显示筛选条件和对应数量
- 激活状态有颜色反转效果
- 响应式设计，移动端垂直排列

## 📁 文件结构
```
project/
├── styles/
│   ├── design-tokens.css     # CSS变量定义
│   └── quick-filter.css      # 快筛徽章样式
└── index.html                # HTML结构
```

## 🎨 CSS变量定义 (design-tokens.css)

```css
:root {
  /* 主色系 */
  --primary-color: #dc2626;
  --primary-hover: #b91c1c;
  
  /* 文本色系 */
  --text-primary: #374151;
  --text-secondary: #6b7280;
  --text-white: #ffffff;
  
  /* 背景色系 */
  --bg-white: #ffffff;
  --bg-gray-50: #f9fafb;
  
  /* 边框色系 */
  --border-color: #e5e7eb;
  
  /* 圆角系统 */
  --radius-full: 9999px;
  
  /* 过渡动画 */
  --transition-slow: 0.3s ease;
}
```

## 🎨 快筛徽章样式 (quick-filter.css)

```css
/* ===== 订单列表快筛徽章样式 ===== */

/* 徽章容器 */
.tabs-vertical-badge {
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
  position: relative;
  flex-wrap: wrap;
  gap: 0;
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
  z-index: 50;
}

/* 徽章项目 */
.tabs-vertical-badge li {
  position: relative;
  margin: 0;
  padding: 0;
}

/* 徽章链接 */
.tabs-vertical-badge li a {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
  width: 100px;
  height: 40px;
  padding: 0 12px;
  margin: 0;
  color: var(--text-secondary);
  background-color: transparent;
  text-decoration: none;
  transition: all var(--transition-slow);
  border: none;
  border-bottom: 1px solid transparent;
  position: relative;
  font-weight: 500;
  font-size: 13px;
  box-sizing: border-box;
  z-index: 100;
}

/* 底线指示器 */
.tabs-vertical-badge li a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: transparent;
  transition: all var(--transition-slow);
}

/* 数字徽章 */
.tabs-vertical-badge .badge {
  background-color: var(--primary-color);
  color: var(--text-white);
  font-size: 10px;
  font-weight: 700;
  padding: 2px 5px;
  border-radius: var(--radius-full);
  transition: all var(--transition-slow);
  min-width: 16px;
  text-align: center;
  line-height: 1;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
  z-index: 1000;
}

/* 激活状态和悬停效果 */
.tabs-vertical-badge li.active a,
.tabs-vertical-badge li a:hover {
  background-color: var(--bg-gray-50);
  color: var(--primary-color);
  border-bottom: 1px solid var(--primary-color);
}

/* 激活状态的底线指示器 */
.tabs-vertical-badge li.active a::before,
.tabs-vertical-badge li a:hover::before {
  background-color: var(--primary-color);
}

/* 激活/悬停时，徽章样式反转 */
.tabs-vertical-badge li.active .badge,
.tabs-vertical-badge li a:hover .badge {
  background-color: var(--text-white);
  color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-vertical-badge {
    flex-direction: column;
    gap: 0;
  }

  .tabs-vertical-badge li {
    margin: 0;
    width: 100%;
  }

  .tabs-vertical-badge li a {
    width: 100%;
    justify-content: flex-start;
    height: 40px;
    padding: 0 16px;
    border: 0;
    border-bottom: 1px solid var(--border-color);
    border-radius: 0;
  }

  .tabs-vertical-badge li:last-child a {
    border-bottom: 0;
  }
}
```

## 📝 HTML结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单列表快筛徽章</title>
    <link rel="stylesheet" href="styles/design-tokens.css">
    <link rel="stylesheet" href="styles/quick-filter.css">
</head>
<body>
    <!-- 快筛徽章容器 -->
    <div class="tabs-vertical-badge">
        <li class="active">
            <a href="#" data-filter="all">
                全单
                <span class="badge">123</span>
            </a>
        </li>
        <li>
            <a href="#" data-filter="pending">
                待审核
                <span class="badge">45</span>
            </a>
        </li>
        <li>
            <a href="#" data-filter="paid">
                已付款
                <span class="badge">32</span>
            </a>
        </li>
        <li>
            <a href="#" data-filter="shipped">
                已发货
                <span class="badge">28</span>
            </a>
        </li>
        <li>
            <a href="#" data-filter="completed">
                已完成
                <span class="badge">18</span>
            </a>
        </li>
    </div>

    <script src="quick-filter.js"></script>
</body>
</html>
```

## 🔧 JavaScript交互逻辑 (quick-filter.js)

```javascript
// 快筛徽章交互功能
class QuickFilter {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
    }

    // 绑定事件
    bindEvents() {
        const badges = document.querySelectorAll('.tabs-vertical-badge li');
        
        badges.forEach(badge => {
            badge.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchFilter(badge);
                
                // 获取筛选条件
                const filterType = badge.querySelector('a').dataset.filter;
                this.onFilterChange(filterType);
            });
        });
    }

    // 切换筛选状态
    switchFilter(activeBadge) {
        // 移除所有激活状态
        document.querySelectorAll('.tabs-vertical-badge li').forEach(badge => {
            badge.classList.remove('active');
        });
        
        // 添加激活状态到当前徽章
        activeBadge.classList.add('active');
    }

    // 筛选条件改变回调（需要根据实际业务实现）
    onFilterChange(filterType) {
        console.log('筛选条件:', filterType);
        
        // 这里实现具体的筛选逻辑
        // 例如：调用API、更新表格数据等
        this.filterOrders(filterType);
    }

    // 筛选订单（示例实现）
    filterOrders(filterType) {
        // 根据筛选类型显示/隐藏订单行
        const orderRows = document.querySelectorAll('.order-row');
        
        orderRows.forEach(row => {
            const orderStatus = row.dataset.status;
            
            if (filterType === 'all' || orderStatus === filterType) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // 更新徽章数量
    updateBadgeCount(filterType, count) {
        const badge = document.querySelector(`[data-filter="${filterType}"] .badge`);
        if (badge) {
            badge.textContent = count;
        }
    }
}

// 初始化快筛功能
document.addEventListener('DOMContentLoaded', () => {
    new QuickFilter();
});
```

## 🎯 AI开发指令

### 1. 基础实现
```
请实现一个订单列表快筛徽章组件，要求：
1. 水平排列的筛选标签，每个标签显示筛选条件和数量
2. 激活状态有颜色反转效果
3. 响应式设计，移动端垂直排列
4. 使用提供的CSS变量系统
5. 包含点击切换和数据筛选功能
```

### 2. 样式定制
```
基于以下设计要求定制快筛徽章样式：
- 主题色：#dc2626（红色）
- 徽章高度：40px
- 徽章宽度：100px
- 数字徽章：圆形，最小宽度16px
- 激活状态：底部红色线条 + 背景色变化
- 过渡动画：0.3s ease
```

### 3. 功能扩展
```
在基础快筛徽章基础上添加以下功能：
1. 支持动态更新徽章数量
2. 支持自定义筛选条件
3. 支持键盘导航（方向键切换）
4. 支持URL参数同步
5. 支持筛选历史记录
```

### 4. 集成指令
```
将快筛徽章集成到现有订单列表页面：
1. 在表格上方添加快筛徽章容器
2. 连接徽章点击事件与表格筛选逻辑
3. 确保样式与现有设计系统一致
4. 添加加载状态和错误处理
5. 优化移动端显示效果
```

## 📱 使用场景

- **订单管理系统**：按订单状态快速筛选
- **用户管理系统**：按用户类型快速筛选
- **产品管理系统**：按产品分类快速筛选
- **任务管理系统**：按任务状态快速筛选

## 🔧 自定义配置

### 颜色主题
```css
/* 蓝色主题 */
--primary-color: #2563eb;
--primary-hover: #1d4ed8;

/* 绿色主题 */
--primary-color: #16a34a;
--primary-hover: #15803d;
```

### 尺寸调整
```css
/* 大尺寸 */
.tabs-vertical-badge li a {
  width: 120px;
  height: 48px;
  font-size: 14px;
}

/* 小尺寸 */
.tabs-vertical-badge li a {
  width: 80px;
  height: 32px;
  font-size: 12px;
}
```

## ✅ 验收标准

1. ✅ 徽章正确显示筛选条件和数量
2. ✅ 点击切换激活状态正常
3. ✅ 激活状态视觉效果正确
4. ✅ 响应式布局在移动端正常
5. ✅ 筛选功能与数据联动正确
6. ✅ 样式与设计系统一致
7. ✅ 无控制台错误和警告

## 🚀 快速开始

1. 复制CSS变量定义到项目中
2. 复制快筛徽章样式文件
3. 在HTML中添加徽章结构
4. 引入JavaScript交互逻辑
5. 根据业务需求调整筛选条件
6. 测试各种状态和交互效果

---

**注意**：此组件基于众跑创新ERP系统设计规范，可根据具体项目需求进行样式和功能调整。