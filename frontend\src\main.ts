import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import './styles/index.scss'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (err, _vm, info) => {
  console.error('Global error:', err, info)
}

// 注册插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, { locale: zhCn })

// 挂载应用
app.mount('#app')

// 初始化主题系统
import { useThemeStore } from './stores/theme'
const themeStore = useThemeStore()
themeStore.init()

// 在开发环境下运行优化验证测试
if (import.meta.env.DEV) {
  import('./utils/test-optimization')
}