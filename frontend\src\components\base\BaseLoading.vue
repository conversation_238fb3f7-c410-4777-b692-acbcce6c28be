<template>
  <div class="base-loading" :class="loadingClass">
    <div class="loading-content">
      <div class="loading-spinner" :style="spinnerStyle">
        <div class="spinner-dot" v-for="i in 12" :key="i" :style="getDotStyle(i)"></div>
      </div>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  text?: string
  size?: 'small' | 'medium' | 'large'
  color?: string
  overlay?: boolean
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  color: 'var(--theme-primary)',
  overlay: false,
  fullscreen: false
})

const loadingClass = computed(() => {
  return {
    [`loading-${props.size}`]: true,
    'loading-overlay': props.overlay,
    'loading-fullscreen': props.fullscreen
  }
})

const spinnerStyle = computed(() => {
  const sizes = {
    small: '20px',
    medium: '32px',
    large: '48px'
  }
  
  return {
    width: sizes[props.size],
    height: sizes[props.size]
  }
})

const getDotStyle = (index: number) => {
  const angle = (index - 1) * 30
  return {
    transform: `rotate(${angle}deg)`,
    animationDelay: `${(index - 1) * 0.1}s`,
    backgroundColor: props.color
  }
}
</script>

<style scoped lang="scss">
.base-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000;
  }
  
  &.loading-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
  }
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $spacing-md;
}

.loading-spinner {
  position: relative;
  display: inline-block;
}

.spinner-dot {
  position: absolute;
  width: 2px;
  height: 25%;
  border-radius: 1px;
  top: 0;
  left: 50%;
  margin-left: -1px;
  transform-origin: 50% 100%;
  animation: spinner-fade 1.2s linear infinite;
}

.loading-text {
  color: var(--theme-text-secondary);
  font-size: $font-size-small;
  text-align: center;
}

.loading-small .loading-text {
  font-size: $font-size-extra-small;
}

.loading-large .loading-text {
  font-size: $font-size-base;
}

@keyframes spinner-fade {
  0%, 39%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}
</style>
