// SCSS 变量定义

// 主色系 - 保持固定值用于SASS函数
$primary-color: #409EFF;
$primary-hover: #66b1ff;
$primary-light: #ecf5ff;
$primary-dark: #337ecc;

// 状态色系 - 保持固定值用于SASS函数
$success-color: #67c23a;
$warning-color: #e6a23c;
$info-color: #909399;
$error-color: #f56c6c;
$danger-color: #f56c6c;

// 文本色系 - 保持固定值用于SASS函数
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;
$text-white: #ffffff;

// 背景色系 - 保持固定值用于SASS函数
$bg-white: #ffffff;
$bg-page: #f2f3f5;
$bg-light: #f5f7fa;
$bg-lighter: #fafafa;

// 边框色系 - 保持固定值用于SASS函数
$border-base: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;
$border-extra-light: #f2f6fc;

// 字体系统
$font-size-extra-small: 12px;
$font-size-small: 13px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

$font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;

// 圆角系统
$border-radius-small: 2px;
$border-radius-base: 4px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// 阴影系统
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 布局尺寸
$header-height: 60px;
$sidebar-width: 200px;
$sidebar-collapsed-width: 40px;
$footer-height: 50px;

// 断点系统
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;
