// 通用样式类

// 文本相关
.text-primary { color: $text-primary; }
.text-regular { color: $text-regular; }
.text-secondary { color: $text-secondary; }
.text-placeholder { color: $text-placeholder; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-ellipsis { @include ellipsis(1); }
.text-ellipsis-2 { @include ellipsis(2); }
.text-ellipsis-3 { @include ellipsis(3); }

// 字体大小
.text-xs { font-size: $font-size-extra-small; }
.text-sm { font-size: $font-size-small; }
.text-base { font-size: $font-size-base; }
.text-md { font-size: $font-size-medium; }
.text-lg { font-size: $font-size-large; }

// 字体粗细
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-bold { font-weight: $font-weight-bold; }

// 布局相关
.flex { @include flex(); }
.flex-center { @include flex(row, center, center); }
.flex-between { @include flex(row, space-between, center); }
.flex-around { @include flex(row, space-around, center); }
.flex-column { @include flex(column); }

// 间距
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

// 导入map模块
@use "sass:map";

// 生成间距类
$spacing-map: (
  xs: $spacing-xs,
  sm: $spacing-sm,
  md: $spacing-md,
  lg: $spacing-lg,
  xl: $spacing-xl,
  xxl: $spacing-xxl
);

@each $size in (xs, sm, md, lg, xl, xxl) {
  $value: map.get($spacing-map, $size);

  .m-#{$size} { margin: $value; }
  .mt-#{$size} { margin-top: $value; }
  .mr-#{$size} { margin-right: $value; }
  .mb-#{$size} { margin-bottom: $value; }
  .ml-#{$size} { margin-left: $value; }

  .p-#{$size} { padding: $value; }
  .pt-#{$size} { padding-top: $value; }
  .pr-#{$size} { padding-right: $value; }
  .pb-#{$size} { padding-bottom: $value; }
  .pl-#{$size} { padding-left: $value; }
}

// 显示/隐藏
.hidden { display: none; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// 圆角
.rounded-sm { border-radius: $border-radius-small; }
.rounded { border-radius: $border-radius-base; }
.rounded-lg { border-radius: $border-radius-round; }
.rounded-full { border-radius: $border-radius-circle; }

// 阴影
.shadow { box-shadow: $box-shadow-base; }
.shadow-light { box-shadow: $box-shadow-light; }
.shadow-dark { box-shadow: $box-shadow-dark; }

// 过渡动画
.transition { transition: $transition-base; }
.transition-fade { transition: $transition-fade; }
