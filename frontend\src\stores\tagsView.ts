import { defineStore } from 'pinia'
import { RouteLocationNormalized } from 'vue-router'

export interface TagView extends Partial<RouteLocationNormalized> {
  title?: string
}

export const useTagsViewStore = defineStore('tagsView', {
  state: () => ({
    visitedViews: [] as TagView[],
    cachedViews: [] as string[]
  }),

  actions: {
    addView(view: TagView) {
      this.addVisitedView(view)
      this.addCachedView(view)
    },

    addVisitedView(view: TagView) {
      if (this.visitedViews.some((v: TagView) => v.path === view.path)) return

      const newView = Object.assign({}, view, {
        title: view.meta?.title || 'no-name'
      })

      // 首页标签固定在第一位，新标签从最后一个标签后面添加
      if (view.meta?.affix) {
        // 如果是固定标签（首页），确保在开头
        const hasHomeTag = this.visitedViews.some(v => v.path === '/home')
        if (!hasHomeTag) {
          this.visitedViews.unshift(newView)
        }
      } else {
        // 非固定标签直接添加到末尾（顺序添加逻辑）
        this.visitedViews.push(newView)
      }
    },

    addCachedView(view: TagView) {
      if (typeof view.name !== 'string') return
      if (this.cachedViews.includes(view.name)) return
      if (!view.meta?.noCache) {
        this.cachedViews.push(view.name)
      }
    },

    delView(view: TagView) {
      return new Promise(resolve => {
        this.delVisitedView(view)
        this.delCachedView(view)
        resolve({
          visitedViews: [...this.visitedViews],
          cachedViews: [...this.cachedViews]
        })
      })
    },

    delVisitedView(view: TagView) {
      return new Promise(resolve => {
        const index = this.visitedViews.findIndex((v: TagView) => v.path === view.path)
        if (index > -1) {
          this.visitedViews.splice(index, 1)
        }
        resolve([...this.visitedViews])
      })
    },

    delCachedView(view: TagView) {
      return new Promise(resolve => {
        if (typeof view.name !== 'string') {
          resolve([...this.cachedViews])
          return
        }
        const index = this.cachedViews.indexOf(view.name)
        if (index > -1) {
          this.cachedViews.splice(index, 1)
        }
        resolve([...this.cachedViews])
      })
    },

    delOthersViews(view: TagView) {
      this.delOthersVisitedViews(view)
      this.delOthersCachedViews(view)
    },

    delOthersVisitedViews(view: TagView) {
      // 保留固定标签（首页）和当前标签
      const affixViews = this.visitedViews.filter((v: TagView) => v.meta?.affix)
      const currentView = this.visitedViews.find((v: TagView) => v.path === view.path)

      this.visitedViews = []

      // 首先添加固定标签
      affixViews.forEach(v => this.visitedViews.push(v))

      // 然后添加当前标签（如果不是固定标签）
      if (currentView && !currentView.meta?.affix) {
        this.visitedViews.push(currentView)
      }
    },

    delOthersCachedViews(view: TagView) {
      if (typeof view.name !== 'string') return
      const index = this.cachedViews.indexOf(view.name)
      if (index > -1) {
        this.cachedViews = this.cachedViews.slice(index, index + 1)
      } else {
        this.cachedViews = []
      }
    },

    updateVisitedView(view: TagView) {
      for (let v of this.visitedViews) {
        if (v.path === view.path) {
          v = Object.assign(v, view)
          break
        }
      }
    },

    // 初始化首页标签
    initAffixTags() {
      // 确保首页标签存在
      const homeView: TagView = {
        path: '/home',
        name: 'Home',
        meta: {
          title: '首页',
          affix: true
        },
        title: '首页'
      }

      // 如果首页标签不存在，添加它
      if (!this.visitedViews.some(v => v.path === '/home')) {
        this.addVisitedView(homeView)
      }
    }
  }
})