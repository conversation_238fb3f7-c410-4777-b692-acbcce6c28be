<template>
  <div class="layout-container">
    <!-- 左侧导航栏 -->
    <div class="sidebar" :class="{ collapsed: appStore.sidebarCollapsed }">
      <div class="logo">
        <h3 v-if="!appStore.sidebarCollapsed">众跑创新</h3>
        <h3 v-else>众跑</h3>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="sidebar-menu"
        background-color="#ffffff"
        text-color="#606266"
        active-text-color="#409EFF"
        :unique-opened="true"
        :collapse="appStore.sidebarCollapsed"
        router
      >
        <template v-for="group in menuGroups" :key="group.groupTitle">
          <div v-if="group.groupTitle" class="menu-group-title">{{ group.groupTitle }}</div>
          <template v-for="item in group.items" :key="item.id">
            <!-- 有子菜单的项目 -->
            <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.id">
              <template #title>
                <el-icon><component :is="getIconComponent(item.icon)" /></el-icon>
                <span>{{ item.title }}</span>
              </template>
              <template v-for="child in item.children" :key="child.id">
                <el-sub-menu v-if="child.children && child.children.length > 0" :index="child.id">
                  <template #title>
                    <el-icon v-if="child.icon"><component :is="getIconComponent(child.icon)" /></el-icon>
                    <span>{{ child.title }}</span>
                  </template>
                  <el-menu-item v-for="subChild in child.children" :key="subChild.id" :index="subChild.route">
                    {{ subChild.title }}
                  </el-menu-item>
                </el-sub-menu>
                <el-menu-item v-else :index="child.route">
                  {{ child.title }}
                </el-menu-item>
              </template>
            </el-sub-menu>
            <!-- 没有子菜单的项目 -->
            <el-menu-item v-else :index="item.route">
              <el-icon><component :is="getIconComponent(item.icon)" /></el-icon>
              <span>{{ item.title }}</span>
            </el-menu-item>
          </template>
        </template>
      </el-menu>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="header">
        <div class="header-left">
          <button class="menu-toggle-btn" @click="toggleSidebar">
            <svg class="menu-icon" viewBox="0 0 24 24" width="18" height="18">
              <path d="M3 6h18v2H3V6zm0 5h18v2H3v-2zm0 5h18v2H3v-2z" fill="currentColor"/>
            </svg>
          </button>
          <span class="breadcrumb">{{ currentPageTitle }}</span>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <!-- 主题切换器 -->
            <ThemeSwitcher />

            <!-- 用户下拉菜单 -->
            <el-dropdown>
              <span class="user-info">
                <el-icon><User /></el-icon>
                管理员
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人中心</el-dropdown-item>
                  <el-dropdown-item>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 页面标签区域 -->
      <tags-view />
      
      <!-- 主要内容区域 -->
      <div class="content">
        <router-view v-slot="{ Component }">
          <keep-alive :include="tagsViewStore.cachedViews">
            <component :is="Component" :key="route.fullPath" />
          </keep-alive>
        </router-view>
      </div>

      <!-- 底部页脚 -->
      <div class="footer">
        <span>© 2024 ERP管理系统 - 版权所有</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import TagsView from './components/TagsView.vue'
import ThemeSwitcher from '@/components/theme/ThemeSwitcher.vue'
import { useTagsViewStore } from '@/stores/tagsView'
import { useAppStore } from '@/stores/app'
import { useThemeStore } from '@/stores/theme'
import { Icons } from '@/utils/icons'

// 解构需要的图标
const {
  House,
  ShoppingCart,
  Headset,
  Box,
  OfficeBuilding,
  Money,
  User,
  ArrowDown,
  PieChart,
  UserFilled,
  Setting,
  Lock,
  Tools,
  Document,
  List,
  Location,
  Warning,
  CircleClose,
  CreditCard,
  Collection,
  Avatar,
  Suitcase,
  Clock,
  Present,
  Key,
  Van,
  Connection
} = Icons

const route = useRoute()
const appStore = useAppStore()
const tagsViewStore = useTagsViewStore()
const themeStore = useThemeStore()

// 初始化主题
themeStore.init()

// 切换侧边栏收起/展开
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 图标映射
const iconMap: Record<string, any> = {
  'home': House,
  'shopping-cart': ShoppingCart,
  'headset': Headset,
  'cube': Box,
  'archive': OfficeBuilding,
  'globe': OfficeBuilding,
  'currency-dollar': Money,
  'users': UserFilled,
  'chart-bar': PieChart,
  'user-group': UserFilled,
  'cog': Setting,
  'shield-check': Lock,
  'adjustments': Tools,
  'document-text': Document,
  'clipboard-list': List,
  'location-marker': Location,
  'exclamation-circle': Warning,
  'ban': CircleClose,
  'office-building': OfficeBuilding,
  'calculator': Money,
  'cash': Money,
  'credit-card': CreditCard,
  'collection': Collection,
  'user-circle': Avatar,
  'briefcase': Suitcase,
  'clock': Clock,
  'heart': Present,
  'gift': Present,
  'key': Key,
  'truck': Van,
  'api': Connection,
  'database': OfficeBuilding,
  'workflow': Tools
}

const getIconComponent = (iconName: string) => {
  return iconMap[iconName] || House
}

// 菜单数据 - 包含首页和订单列表
const menuGroups = ref([
  {
    groupTitle: "",
    items: [
      {
        id: "homepage",
        title: "首页",
        icon: "home",
        route: "/home",
        children: []
      },
      {
        id: "sales-management",
        title: "销售管理",
        icon: "shopping-cart",
        route: null,
        children: [
          {
            id: "order-list",
            title: "订单列表",
            route: "/sales/orders-new"
          }
        ]
      },
      {
        id: "system-management",
        title: "系统管理",
        icon: "setting",
        route: null,
        children: [
          {
            id: "theme-settings",
            title: "主题设置",
            route: "/system/theme"
          }
        ]
      }
    ]
  }
])

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || '首页'
})
</script>

<style scoped>
.layout-container {
  display: flex;
  height: 100vh;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.sidebar {
  width: 160px;
  background-color: var(--theme-background, #ffffff);
  color: var(--theme-text-secondary, #606266);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--theme-border, #e4e7ed);
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 40px;
}

.logo {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme-background, #ffffff);
  border-bottom: 1px solid var(--theme-border, #e4e7ed);
}

.logo h3 {
  color: var(--theme-text, #303133);
  font-size: 14px;
  margin: 0;
}

.sidebar-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

.menu-group-title {
  padding: 8px 16px;
  font-size: 12px;
  color: var(--theme-text-secondary, #909399);
  background-color: var(--theme-surface, #f5f7fa);
  border-bottom: 1px solid var(--theme-border, #e4e7ed);
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 0;
  min-width: 0;
  overflow: hidden;
}

.header {
  height: 40px;
  background-color: var(--theme-background, #fff);
  border-bottom: 1px solid var(--theme-border, #e4e7ed);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  transition: all 0.2s ease;
}

.menu-toggle-btn:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}

.menu-icon {
  display: block;
}

.header-left .breadcrumb {
  font-size: 14px;
  color: var(--theme-text, #303133);
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-right .user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: var(--theme-text-secondary, #606266);
}

.header-right .user-info .el-icon {
  margin: 0 4px;
}

.content {
  flex: 1;
  padding: 0;
  background-color: var(--theme-surface, #f5f5f5);
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.footer {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--theme-text-secondary, #909399);
  background-color: var(--theme-background, #ffffff);
  border-top: 1px solid var(--theme-border, #e4e7ed);
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-sub-menu .el-menu-item) {
  padding-left: 40px !important;
  min-height: 36px;
  line-height: 36px;
}

:deep(.el-menu-item) {
  min-height: 36px;
  line-height: 36px;
}

:deep(.el-sub-menu__title) {
  min-height: 36px;
  line-height: 36px;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  height: 40px;
  line-height: 40px;
  margin: 0;
  padding-top: 0;
  padding-bottom: 0;
}

/* 收起状态下的样式 */
.sidebar.collapsed .sidebar-menu :deep(.el-menu) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  overflow: hidden !important;
}

.sidebar.collapsed .sidebar-menu :deep(.el-menu-item),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu__title) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 0 !important;
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  overflow: hidden !important;
}

.sidebar.collapsed .sidebar-menu :deep(.el-menu-item .el-icon),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu__title .el-icon) {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.sidebar.collapsed .sidebar-menu :deep(.el-menu-item span),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu__title span) {
  display: none !important;
}

.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu .el-icon) {
  margin-right: 0 !important;
}

.sidebar.collapsed .menu-group-title {
  display: none !important;
}

/* 强制覆盖Element Plus的默认宽度 */
.sidebar.collapsed .sidebar-menu :deep(.el-menu--collapse) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  overflow: hidden !important;
}

.sidebar.collapsed .sidebar-menu :deep(.el-menu--collapse .el-menu-item),
.sidebar.collapsed .sidebar-menu :deep(.el-menu--collapse .el-sub-menu__title) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/* 强制控制所有可能的内部容器 */
.sidebar.collapsed .sidebar-menu :deep(*) {
  max-width: 40px !important;
}

/* 特别处理Element Plus菜单的内部结构 */
.sidebar.collapsed .sidebar-menu :deep(.el-menu-item > *),
.sidebar.collapsed .sidebar-menu :deep(.el-sub-menu__title > *) {
  max-width: 40px !important;
  overflow: hidden !important;
}

/* 确保整个侧边栏容器严格控制宽度 */
.sidebar.collapsed {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  overflow: hidden !important;
}
</style>