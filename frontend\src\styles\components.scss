// 组件样式

// 页面容器
.page-container {
  padding: $spacing-lg;
  background-color: $bg-page;
  min-height: calc(100vh - #{$header-height} - #{$footer-height});
}

// 卡片容器
.card-container {
  background-color: $bg-white;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
}

// 表格容器
.table-container {
  background-color: $bg-white;
  border-radius: $border-radius-base;
  overflow: hidden;
  
  .el-table {
    border: none;
    
    .el-table__header {
      background-color: $bg-light;
    }
  }
}

// 搜索栏
.search-bar {
  background-color: $bg-white;
  padding: $spacing-lg;
  border-radius: $border-radius-base;
  margin-bottom: $spacing-lg;
  box-shadow: $box-shadow-light;
  
  .el-form-item {
    margin-bottom: 0;
  }
}

// 操作栏
.action-bar {
  background-color: $bg-white;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-base;
  margin-bottom: $spacing-lg;
  box-shadow: $box-shadow-light;
  @include flex(row, space-between, center);
  
  .action-left {
    @include flex(row, flex-start, center);
    gap: $spacing-md;
  }
  
  .action-right {
    @include flex(row, flex-end, center);
    gap: $spacing-md;
  }
}

// 状态标签 - 使用固定颜色值
.status-tag {
  padding: 2px 8px;
  border-radius: $border-radius-small;
  font-size: $font-size-extra-small;
  font-weight: $font-weight-medium;

  &.status-success {
    background-color: rgba(103, 194, 58, 0.2);
    color: $success-color;
  }

  &.status-warning {
    background-color: rgba(230, 162, 60, 0.2);
    color: $warning-color;
  }

  &.status-danger {
    background-color: rgba(245, 108, 108, 0.2);
    color: $danger-color;
  }

  &.status-info {
    background-color: rgba(144, 147, 153, 0.2);
    color: $info-color;
  }
}

// 加载状态
.loading-container {
  @include flex(column, center, center);
  padding: $spacing-xxl;
  color: $text-secondary;
}

// 空状态
.empty-container {
  @include flex(column, center, center);
  padding: $spacing-xxl * 2;
  color: $text-secondary;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: $spacing-lg;
    color: $text-placeholder;
  }
  
  .empty-text {
    font-size: $font-size-base;
    margin-bottom: $spacing-md;
  }
  
  .empty-description {
    font-size: $font-size-small;
    color: $text-placeholder;
  }
}
