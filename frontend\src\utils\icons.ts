// 统一的图标管理
import {
  House,
  ShoppingCart,
  Headset,
  Box,
  OfficeBuilding,
  Money,
  User,
  ArrowDown,
  PieChart,
  UserFilled,
  Setting,
  Lock,
  Tools,
  Document,
  List,
  Location,
  Warning,
  CircleClose,
  CreditCard,
  Collection,
  Avatar,
  Suitcase,
  Clock,
  Present,
  Key,
  Van,
  Connection,
  Menu,
  Fold,
  Expand
} from '@element-plus/icons-vue'

// 导航相关图标
export const NavigationIcons = {
  House,
  ShoppingCart,
  Box,
  Money,
  Setting,
  User,
  ArrowDown,
  Menu,
  Fold,
  Expand
} as const

// 业务相关图标
export const BusinessIcons = {
  PieChart,
  Document,
  List,
  Location,
  CreditCard,
  Collection,
  Suitcase,
  Clock,
  Present,
  Van,
  Connection
} as const

// 状态相关图标
export const StatusIcons = {
  Warning,
  CircleClose,
  Lock,
  Tools
} as const

// 用户相关图标
export const UserIcons = {
  User,
  UserFilled,
  Avatar,
  Key
} as const

// 系统相关图标
export const SystemIcons = {
  Headset,
  OfficeBuilding
} as const

// 导出所有图标
export const Icons = {
  ...NavigationIcons,
  ...BusinessIcons,
  ...StatusIcons,
  ...UserIcons,
  ...SystemIcons
} as const

// 图标类型
export type IconName = keyof typeof Icons
