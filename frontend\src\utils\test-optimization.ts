// 代码优化验证测试
// 用于验证优化后的功能是否正常工作

import { Icons } from './icons'
import { useLoading, usePagination, useMessage } from './composables'
import { setStorage, getStorage } from './storage'
import type { ThemeType } from '@/types'

/**
 * 测试图标管理系统
 */
export function testIconSystem() {
  console.log('🔍 测试图标管理系统...')
  
  // 测试图标是否正确导入
  const testIcons = ['House', 'ShoppingCart', 'User', 'Setting'] as const
  
  testIcons.forEach(iconName => {
    if (Icons[iconName]) {
      console.log(`✅ 图标 ${iconName} 导入成功`)
    } else {
      console.error(`❌ 图标 ${iconName} 导入失败`)
    }
  })
  
  console.log(`📊 总计可用图标数量: ${Object.keys(Icons).length}`)
}

/**
 * 测试组合式函数
 */
export function testComposables() {
  console.log('🔍 测试组合式函数...')
  
  try {
    // 测试加载状态管理
    const { loading, setLoading } = useLoading()
    setLoading(true)
    console.log('✅ useLoading 函数正常')
    
    // 测试分页管理
    const { currentPage, handlePageChange } = usePagination()
    handlePageChange(2)
    console.log('✅ usePagination 函数正常')
    
    // 测试消息提示
    const { success } = useMessage()
    console.log('✅ useMessage 函数正常')
    
  } catch (error) {
    console.error('❌ 组合式函数测试失败:', error)
  }
}

/**
 * 测试存储系统
 */
export function testStorageSystem() {
  console.log('🔍 测试存储系统...')
  
  try {
    // 测试localStorage
    const testData = { test: 'optimization', timestamp: Date.now() }
    setStorage('test-key', testData)
    const retrieved = getStorage('test-key')
    
    if (JSON.stringify(retrieved) === JSON.stringify(testData)) {
      console.log('✅ localStorage 存储系统正常')
    } else {
      console.error('❌ localStorage 存储系统异常')
    }
    
    // 清理测试数据
    localStorage.removeItem('test-key')
    
  } catch (error) {
    console.error('❌ 存储系统测试失败:', error)
  }
}

/**
 * 测试主题系统
 */
export function testThemeSystem() {
  console.log('🔍 测试主题系统...')
  
  try {
    // 检查CSS变量是否存在
    const rootStyles = getComputedStyle(document.documentElement)
    const themeVariables = [
      '--theme-primary',
      '--theme-background',
      '--theme-text',
      '--theme-border'
    ]
    
    themeVariables.forEach(variable => {
      const value = rootStyles.getPropertyValue(variable)
      if (value) {
        console.log(`✅ CSS变量 ${variable} 存在: ${value.trim()}`)
      } else {
        console.warn(`⚠️ CSS变量 ${variable} 未找到`)
      }
    })
    
  } catch (error) {
    console.error('❌ 主题系统测试失败:', error)
  }
}

/**
 * 运行所有优化验证测试
 */
export function runOptimizationTests() {
  console.log('🚀 开始运行代码优化验证测试...')
  console.log('=' .repeat(50))
  
  testIconSystem()
  console.log('')
  
  testComposables()
  console.log('')
  
  testStorageSystem()
  console.log('')
  
  testThemeSystem()
  console.log('')
  
  console.log('=' .repeat(50))
  console.log('✨ 代码优化验证测试完成!')
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保DOM加载完成
  setTimeout(() => {
    runOptimizationTests()
  }, 1000)
}
