// ===========================================
// 订单列表组件样式 - Order List Component Styles
// ===========================================

// 导入样式系统
@import '../variables.scss';
@import '../mixins.scss';

/* 主容器 - 使用零间距设计 */
.order-list-container {
  @include container-reset;
  background-color: $bg-light;
  height: 100%;
  @include flex($direction: column);
}

/* 快筛徽章容器零间距 */
:deep(.quick-filter-badges) {
  @include reset-element;
}

/* 搜索筛选栏 - 弹性布局，160px固定宽度，自动换行 */
.search-filter-bar {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: stretch !important;
  background-color: $bg-white;
  border: 1px solid $border-lighter;
  @include no-radius;
  overflow: visible !important;
  min-height: 30px;
  width: 100% !important;
  max-width: none !important;
  padding: 8px 0 8px 16px !important; // 垂直间距8px，左边间距16px
  margin: 0 !important;
  gap: 8px !important; // 元素之间间距8px

  // 每个子元素的基础样式 - 固定宽度160px
  > * {
    flex: 0 0 160px !important; // 固定宽度160px，不伸缩
    width: 160px !important; // 明确设置宽度
    height: 30px !important; // 统一高度
    margin: 0 !important; // 清除所有margin，使用gap控制间距
    box-sizing: border-box !important;
  }

  // 时间选择器特殊处理 - 占用更多空间
  > .el-date-editor,
  > .date-range-picker {
    flex: 0 0 320px !important; // 固定宽度320px（2倍160px）
    width: 320px !important;
  }

  // 内部元素样式 - 统一所有输入组件
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-editor),
  :deep(.searchable-select) {
    width: 100% !important;
    height: 30px !important;

    .el-input__wrapper,
    .el-select__wrapper {
      @include no-radius;
      @include no-shadow;
      border: none;
      border-bottom: 1px solid $border-lighter;
      border-right: 1px solid $border-lighter;
      height: 30px !important;
      min-height: 30px !important;
      display: flex !important;
      align-items: center !important;
      box-sizing: border-box;
      width: 100% !important;
    }
  }

  // 时间选择器样式
  :deep(.el-date-editor) {
    @include no-radius;
    @include no-shadow;
    border: none;
    border-bottom: 1px solid $border-lighter;
    border-right: 1px solid $border-lighter;
    height: 30px !important;
    min-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    box-sizing: border-box;
    width: 100% !important;
  }

  // 时间选择器内部元素
  :deep(.el-date-editor) {
    .el-range-input {
      width: 45% !important;
      height: 30px !important;
      line-height: 30px !important;
      font-size: $font-size-extra-small;
      border: none !important;
      padding: 0 4px !important;
    }

    .el-range-separator {
      width: 10% !important;
      height: 30px !important;
      line-height: 30px !important;
      text-align: center;
      font-size: $font-size-extra-small;
    }
  }
  
  // 弹性布局边框处理 - 简化版本
  > *:last-child {
    .el-input__wrapper,
    .el-select__wrapper {
      border-right: none;
    }

    &.el-date-editor {
      border-right: none;
    }
  }

  // 弹性布局自动换行，无需复杂的响应式边框规则

  // 最后一行移除底边框 - 响应式
  // 默认 12列: 18个元素，第二行从第13个开始
  > *:nth-child(n+13) {
    .el-input__wrapper,
    .el-select__wrapper {
      border-bottom: none;
    }

    &.el-date-editor {
      border-bottom: none;
    }
  }

  // 大屏幕 8列: 第三行从第17个开始
  @include respond-to(lg) {
    > *:nth-child(n+13) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: 1px solid $border-lighter;
      }

      &.el-date-editor {
        border-bottom: 1px solid $border-lighter;
      }
    }

    > *:nth-child(n+17) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: none;
      }

      &.el-date-editor {
        border-bottom: none;
      }
    }
  }

  // 中等屏幕 6列: 第四行从第19个开始 (但只有18个元素，所以都在前三行)
  @include respond-to(md) {
    > *:nth-child(n+17) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: 1px solid $border-lighter;
      }

      &.el-date-editor {
        border-bottom: 1px solid $border-lighter;
      }
    }

    > *:nth-child(n+13) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: none;
      }

      &.el-date-editor {
        border-bottom: none;
      }
    }
  }

  // 小屏幕 4列: 第五行从第17个开始
  @include respond-to(sm) {
    > *:nth-child(n+13) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: 1px solid $border-lighter;
      }

      &.el-date-editor {
        border-bottom: 1px solid $border-lighter;
      }
    }

    > *:nth-child(n+17) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: none;
      }

      &.el-date-editor {
        border-bottom: none;
      }
    }
  }

  // 超小屏幕 2列: 第十行从第19个开始 (但只有18个元素)
  @include respond-to(xs) {
    > *:nth-child(n+17) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: 1px solid $border-lighter;
      }

      &.el-date-editor {
        border-bottom: 1px solid $border-lighter;
      }
    }

    > *:nth-child(n+18) {
      .el-input__wrapper,
      .el-select__wrapper {
        border-bottom: none;
      }

      &.el-date-editor {
        border-bottom: none;
      }
    }
  }
}

/* 操作栏 - 零间距设计 */
.action-bar {
  @include reset-element;
  
  :deep(.el-card__body) {
    @include no-spacing;
    height: 100%;
    box-sizing: border-box;
    @include flex-start;
  }
}

/* 全局卡片重置 */
:deep(.el-card) {
  @include reset-el-card;
  
  &.no-border {
    @include reset-element;
  }
}

/* 表格区域 - 弹性布局和零间距 */
.table-section {
  flex: 1;
  min-height: 0;
  @include flex($direction: column);
  @include no-spacing;
  
  .el-card {
    flex: 1;
    @include flex($direction: column);
    min-height: 0;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    @include reset-element;
  }
  
  :deep(.el-card__body) {
    flex: 1;
    @include flex($direction: column);
    min-height: 0;
    overflow: hidden;
    @include no-spacing;
  }
}

/* 分页容器 */
.pagination-container {
  @include no-spacing;
  @include flex-end;
}

/* 表格样式优化 */
:deep(.el-table) {
  flex: 1;
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  @include no-spacing;
  table-layout: fixed;
  
  .el-table__header-wrapper {
    @include no-spacing;
  }
  
  .el-table__body-wrapper {
    @include no-spacing;
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: auto;
  }
  
  .el-table__footer-wrapper {
    @include no-spacing;
    width: 100%;
    max-width: 100%;
  }
  
  .el-table__cell {
    margin: 0;
    padding: $spacing-sm $spacing-md;
  }
  
  // 表格边框间距设置为0
  border-spacing: 0;
  border-collapse: collapse;
  
  table {
    border-spacing: 0;
    border-collapse: collapse;
  }
}

/* 表格单元格样式 - 使用变量和工具类 */
.order-no-cell,
.order-source-cell,
.product-spec-cell,
.warehouse-cell,
.logistics-cell {
  @include flex($direction: column);
  gap: $spacing-xs;
  font-size: $font-size-extra-small;
  line-height: $line-height-normal;
}

.order-no-cell {
  .internal-order {
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
  
  .external-order {
    color: $text-secondary;
  }
}

.order-source-cell {
  .shop-name {
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
  
  .platform-name {
    color: $text-secondary;
  }
}

.product-spec-cell {
  .product-code {
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
  
  .product-size {
    color: $text-regular;
  }
  
  .product-quantity {
    color: $primary-color;
    font-weight: $font-weight-bold;
  }
}

.warehouse-cell {
  .warehouse-name {
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
  
  .channel-name {
    color: $text-secondary;
  }
}

/* 收货信息样式 */
.shipping-info-cell {
  position: relative;
  font-size: $font-size-extra-small;
  line-height: $line-height-normal;
  padding-top: $spacing-xxl;
}

.copy-btn-container {
  position: absolute;
  top: 0;
  right: 0;
}

.copy-btn {
  background-color: $text-primary;
  color: $text-white;
  border-color: $text-primary;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-extra-small;
  height: auto;
  min-height: $button-height-sm;
  line-height: $line-height-tight;
  
  &:hover,
  &:focus {
    background-color: mix(black, $text-primary, 10%);
    border-color: mix(black, $text-primary, 10%);
    color: $text-white;
  }
}

.shipping-info-cell {
  .recipient {
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: 2px;
  }
  
  .phone {
    color: $text-regular;
    margin-bottom: 2px;
  }
  
  .address {
    color: $text-regular;
    margin-bottom: 2px;
    @include text-break;
  }
  
  .internal-order-ref {
    color: $text-secondary;
    font-size: $font-size-xs;
  }
}

/* 物流信息样式 */
.logistics-cell {
  .logistics-company {
    font-weight: $font-weight-bold;
    color: $text-primary;
  }
  
  .tracking-no {
    color: $text-regular;
    font-family: monospace;
  }
}

/* 金额信息样式 */
.amount-cell {
  @include flex($direction: column);
  gap: 2px;
  font-size: $font-size-base;
  line-height: 1.5;
  
  .amount-item {
    @include flex-between;
  }
  
  .label {
    color: $text-secondary;
    font-size: $font-size-extra-small;
    min-width: 40px;
  }
}

/* 备注样式 */
.remarks-cell {
  @include flex($direction: column);
  gap: $spacing-xs;
  font-size: $font-size-extra-small;
  line-height: $line-height-normal;
  
  .remark-item {
    @include flex($align: flex-start);
    gap: $spacing-xs;
  }
  
  .label {
    color: $text-secondary;
    font-size: $font-size-xs;
    min-width: 30px;
    flex-shrink: 0;
  }
}

/* 时间信息样式 */
.time-info-cell {
  @include flex($direction: column);
  gap: 2px;
  font-size: $font-size-base;
  line-height: 1.5;

  .time-item {
    @include flex-start;
    gap: $spacing-xs;
  }

  .label {
    color: $text-secondary;
    font-size: $font-size-extra-small;
    min-width: 40px;
    flex-shrink: 0;
  }
}

/* 表格行和单元格样式优化 */
:deep(.el-table) {
  .el-table__row {
    height: auto;
  }

  .el-table__cell {
    padding: $spacing-sm $spacing-xs;
    vertical-align: top;
  }

  // 复选框列样式
  .el-table-column--selection > .el-table__cell {
    padding-left: 0;
    padding-right: 0;
    vertical-align: middle;
  }

  // 表格头部样式
  .el-table__header-wrapper .el-table__cell {
    background-color: $bg-light;
    font-weight: $font-weight-bold;
    font-size: $font-size-extra-small;
    padding: $spacing-sm $spacing-xs;
    text-align: center;
  }
}

/* 响应式调整 */
@include respond-to(lg) {
  .amount-cell,
  .time-info-cell {
    font-size: $font-size-xs;
  }

  .operation-row .el-button {
    font-size: 9px;
  }
}

/* 快筛徽章样式 */
.tabs-vertical-badge {
  @include flex($wrap: wrap);
  gap: $spacing-md;
  background-color: $bg-white;
  padding: $spacing-lg;
  border-bottom: none;

  :deep(.el-input__wrapper:hover) {
    box-shadow: 0 0 0 1px $border-base inset;
  }
}

/* 表格内部边框 */
.internal-border-right {
  :deep(th.el-table__cell),
  :deep(td.el-table__cell) {
    border-right: 1px solid var(--el-table-border-color);

    &:last-of-type {
      border-right: none;
    }
  }
}
